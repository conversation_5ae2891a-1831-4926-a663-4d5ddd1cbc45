using System;
using System.Collections.Generic;
using MySql.Data.MySqlClient;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan.DAL
{
    public class BookingDAO
    {
        public bool CreateBooking(Booking booking)
        {
            try
            {
                string query = @"INSERT INTO Bookings (RoomId, UserId, CheckInDate, CheckOutDate, 
                               Status, TotalAmount, BookingDate, CustomerName, CustomerEmail) 
                               VALUES (@RoomId, @UserId, @CheckInDate, @CheckOutDate, 
                               @Status, @TotalAmount, @BookingDate, @CustomerName, @CustomerEmail)";

                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@RoomId", booking.RoomId),
                    new MySqlParameter("@UserId", booking.UserId),
                    new MySqlParameter("@CheckInDate", booking.CheckInDate),
                    new MySqlParameter("@CheckOutDate", booking.CheckOutDate),
                    new MySqlParameter("@Status", booking.Status),
                    new MySqlParameter("@TotalAmount", booking.TotalAmount),
                    new MySqlParameter("@BookingDate", booking.BookingDate),
                    new MySqlParameter("@CustomerName", booking.CustomerName),
                    new MySqlParameter("@CustomerEmail", booking.CustomerEmail)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating booking: {ex.Message}");
                return false;
            }
        }

        public List<Booking> GetBookingsByUserId(int userId)
        {
            var bookings = new List<Booking>();
            try
            {
                string query = @"SELECT b.BookingId, b.RoomId, b.UserId, b.CheckInDate, b.CheckOutDate, 
                               b.Status, b.TotalAmount, b.BookingDate, b.CustomerName, b.CustomerEmail,
                               r.RoomNumber, r.RoomType, r.Price
                               FROM Bookings b
                               INNER JOIN Rooms r ON b.RoomId = r.RoomId
                               WHERE b.UserId = @UserId
                               ORDER BY b.BookingDate DESC";
                
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var reader = DatabaseHelper.ExecuteReader(query, connection, new MySqlParameter("@UserId", userId)))
                    {
                        while (reader.Read())
                        {
                            var booking = new Booking
                            {
                                BookingId = reader.GetInt32("BookingId"),
                                RoomId = reader.GetInt32("RoomId"),
                                UserId = reader.GetInt32("UserId"),
                                CheckInDate = reader.GetDateTime("CheckInDate"),
                                CheckOutDate = reader.GetDateTime("CheckOutDate"),
                                Status = reader.GetString("Status"),
                                TotalAmount = reader.GetDecimal("TotalAmount"),
                                BookingDate = reader.GetDateTime("BookingDate"),
                                CustomerName = reader.GetString("CustomerName"),
                                CustomerEmail = reader.GetString("CustomerEmail"),
                                Room = new Room
                                {
                                    RoomId = reader.GetInt32("RoomId"),
                                    RoomNumber = reader.GetString("RoomNumber"),
                                    RoomType = reader.GetString("RoomType"),
                                    Price = reader.GetDecimal("Price")
                                }
                            };
                            bookings.Add(booking);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting bookings by user ID: {ex.Message}");
            }
            return bookings;
        }

        public Booking GetBookingById(int bookingId)
        {
            try
            {
                string query = @"SELECT b.BookingId, b.RoomId, b.UserId, b.CheckInDate, b.CheckOutDate, 
                               b.Status, b.TotalAmount, b.BookingDate, b.CustomerName, b.CustomerEmail,
                               r.RoomNumber, r.RoomType, r.Price, r.Description
                               FROM Bookings b
                               INNER JOIN Rooms r ON b.RoomId = r.RoomId
                               WHERE b.BookingId = @BookingId";
                
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var reader = DatabaseHelper.ExecuteReader(query, connection, new MySqlParameter("@BookingId", bookingId)))
                    {
                        if (reader.Read())
                        {
                            return new Booking
                            {
                                BookingId = reader.GetInt32("BookingId"),
                                RoomId = reader.GetInt32("RoomId"),
                                UserId = reader.GetInt32("UserId"),
                                CheckInDate = reader.GetDateTime("CheckInDate"),
                                CheckOutDate = reader.GetDateTime("CheckOutDate"),
                                Status = reader.GetString("Status"),
                                TotalAmount = reader.GetDecimal("TotalAmount"),
                                BookingDate = reader.GetDateTime("BookingDate"),
                                CustomerName = reader.GetString("CustomerName"),
                                CustomerEmail = reader.GetString("CustomerEmail"),
                                Room = new Room
                                {
                                    RoomId = reader.GetInt32("RoomId"),
                                    RoomNumber = reader.GetString("RoomNumber"),
                                    RoomType = reader.GetString("RoomType"),
                                    Price = reader.GetDecimal("Price"),
                                    Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description")
                                }
                            };
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting booking by ID: {ex.Message}");
                return null;
            }
        }

        public bool UpdateBookingStatus(int bookingId, string status)
        {
            try
            {
                string query = "UPDATE Bookings SET Status = @Status WHERE BookingId = @BookingId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@Status", status),
                    new MySqlParameter("@BookingId", bookingId)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating booking status: {ex.Message}");
                return false;
            }
        }

        public List<Booking> GetAllBookings()
        {
            var bookings = new List<Booking>();
            try
            {
                string query = @"SELECT b.BookingId, b.RoomId, b.UserId, b.CheckInDate, b.CheckOutDate, 
                               b.Status, b.TotalAmount, b.BookingDate, b.CustomerName, b.CustomerEmail,
                               r.RoomNumber, r.RoomType, r.Price
                               FROM Bookings b
                               INNER JOIN Rooms r ON b.RoomId = r.RoomId
                               ORDER BY b.BookingDate DESC";
                
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var reader = DatabaseHelper.ExecuteReader(query, connection))
                    {
                        while (reader.Read())
                        {
                            var booking = new Booking
                            {
                                BookingId = reader.GetInt32("BookingId"),
                                RoomId = reader.GetInt32("RoomId"),
                                UserId = reader.GetInt32("UserId"),
                                CheckInDate = reader.GetDateTime("CheckInDate"),
                                CheckOutDate = reader.GetDateTime("CheckOutDate"),
                                Status = reader.GetString("Status"),
                                TotalAmount = reader.GetDecimal("TotalAmount"),
                                BookingDate = reader.GetDateTime("BookingDate"),
                                CustomerName = reader.GetString("CustomerName"),
                                CustomerEmail = reader.GetString("CustomerEmail"),
                                Room = new Room
                                {
                                    RoomId = reader.GetInt32("RoomId"),
                                    RoomNumber = reader.GetString("RoomNumber"),
                                    RoomType = reader.GetString("RoomType"),
                                    Price = reader.GetDecimal("Price")
                                }
                            };
                            bookings.Add(booking);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting all bookings: {ex.Message}");
            }
            return bookings;
        }
    }
}
