using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using QuanLyKhachSan.BLL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan
{
    public partial class MyBookings : System.Web.UI.Page
    {
        private BookingManager bookingManager;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in
            if (Session["UserId"] == null)
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            bookingManager = new BookingManager();

            if (!IsPostBack)
            {
                LoadUserBookings();
                LoadStatistics();
            }
        }

        private void LoadUserBookings()
        {
            try
            {
                int userId = Convert.ToInt32(Session["UserId"]);
                var bookings = bookingManager.GetUserBookingsWithDetails(userId);

                // Apply filters
                bookings = ApplyFilters(bookings);

                // Sort by booking date descending
                bookings = bookings.OrderByDescending(b => b.BookingDate).ToList();

                if (bookings.Any())
                {
                    rptBookings.DataSource = bookings;
                    rptBookings.DataBind();
                    pnlNoBookings.Visible = false;
                }
                else
                {
                    rptBookings.DataSource = null;
                    rptBookings.DataBind();
                    pnlNoBookings.Visible = true;
                }
            }
            catch (Exception ex)
            {
                ShowMessage("Có lỗi xảy ra khi tải danh sách đặt phòng.", "alert-danger");
                System.Diagnostics.Debug.WriteLine($"Error loading user bookings: {ex.Message}");
            }
        }

        private List<Booking> ApplyFilters(List<Booking> bookings)
        {
            // Filter by status
            string statusFilter = ddlStatusFilter.SelectedValue;
            if (!string.IsNullOrEmpty(statusFilter))
            {
                bookings = bookings.Where(b => b.Status == statusFilter).ToList();
            }

            // Filter by date range
            if (!string.IsNullOrEmpty(txtFromDate.Text))
            {
                DateTime fromDate = DateTime.Parse(txtFromDate.Text);
                bookings = bookings.Where(b => b.BookingDate.Date >= fromDate.Date).ToList();
            }

            if (!string.IsNullOrEmpty(txtToDate.Text))
            {
                DateTime toDate = DateTime.Parse(txtToDate.Text);
                bookings = bookings.Where(b => b.BookingDate.Date <= toDate.Date).ToList();
            }

            return bookings;
        }

        private void LoadStatistics()
        {
            try
            {
                int userId = Convert.ToInt32(Session["UserId"]);
                var allBookings = bookingManager.GetUserBookingsWithDetails(userId);

                lblTotalBookings.Text = allBookings.Count.ToString();

                var confirmedBookings = allBookings.Where(b => b.Status == "Confirmed" || b.Status == "Completed").ToList();
                lblConfirmedBookings.Text = confirmedBookings.Count.ToString();

                var totalSpent = confirmedBookings.Sum(b => b.TotalAmount);
                lblTotalSpent.Text = totalSpent.ToString("N0");

                var totalNights = confirmedBookings.Sum(b => (b.CheckOutDate - b.CheckInDate).Days);
                lblTotalNights.Text = totalNights.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading statistics: {ex.Message}");
            }
        }

        protected void ddlStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadUserBookings();
        }

        protected void btnFilter_Click(object sender, EventArgs e)
        {
            LoadUserBookings();
        }

        protected void btnClearFilter_Click(object sender, EventArgs e)
        {
            ddlStatusFilter.SelectedIndex = 0;
            txtFromDate.Text = "";
            txtToDate.Text = "";
            LoadUserBookings();
        }

        protected void rptBookings_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            int bookingId = Convert.ToInt32(e.CommandArgument);

            if (e.CommandName == "ViewDetails")
            {
                Response.Redirect($"BookingDetails.aspx?id={bookingId}");
            }
            else if (e.CommandName == "CancelBooking")
            {
                try
                {
                    int userId = Convert.ToInt32(Session["UserId"]);
                    bool success = bookingManager.CancelBooking(bookingId, userId);

                    if (success)
                    {
                        ShowMessage("Đặt phòng đã được hủy thành công.", "alert-success");
                        LoadUserBookings();
                        LoadStatistics();
                    }
                    else
                    {
                        ShowMessage("Không thể hủy đặt phòng. Vui lòng kiểm tra lại.", "alert-danger");
                    }
                }
                catch (Exception ex)
                {
                    ShowMessage("Có lỗi xảy ra khi hủy đặt phòng.", "alert-danger");
                    System.Diagnostics.Debug.WriteLine($"Error cancelling booking: {ex.Message}");
                }
            }
        }

        protected string GetStatusBadgeClass(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "bg-success";
                case "pending":
                    return "bg-warning text-dark";
                case "cancelled":
                    return "bg-danger";
                case "completed":
                    return "bg-info";
                default:
                    return "bg-secondary";
            }
        }

        protected string GetStatusText(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "Đã xác nhận";
                case "pending":
                    return "Chờ xác nhận";
                case "cancelled":
                    return "Đã hủy";
                case "completed":
                    return "Hoàn thành";
                default:
                    return status;
            }
        }

        protected bool CanCancelBooking(string status, DateTime checkInDate)
        {
            // Can only cancel if status is Confirmed or Pending and check-in is at least 24 hours away
            return (status == "Confirmed" || status == "Pending") && 
                   checkInDate.Date > DateTime.Today.AddDays(1);
        }

        protected int GetNumberOfNights(DateTime checkIn, DateTime checkOut)
        {
            return (checkOut - checkIn).Days;
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }
    }
}
