-- Tạo database cho hệ thống đặt phòng khách sạn
CREATE DATABASE IF NOT EXISTS HotelBookingDB;
USE HotelBookingDB;

-- Tạo bảng Users (Ngườ<PERSON> dùng)
CREATE TABLE Users (
    UserId INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Email VARCHAR(100) NOT NULL UNIQUE,
    PasswordHash VARCHAR(255) NOT NULL,
    Role VARCHAR(20) NOT NULL DEFAULT 'Customer',
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (Email),
    INDEX idx_role (Role)
);

-- Tạo bảng Rooms (Phòng)
CREATE TABLE Rooms (
    RoomId INT AUTO_INCREMENT PRIMARY KEY,
    RoomNumber VARCHAR(10) NOT NULL UNIQUE,
    RoomType VARCHAR(50) NOT NULL,
    Price DECIMAL(10,2) NOT NULL,
    Description TEXT,
    IsAvailable BOOLEAN NOT NULL DEFAULT TRUE,
    ImageUrl VARCHAR(255),
    MaxOccupancy INT NOT NULL DEFAULT 2,
    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_room_number (RoomNumber),
    INDEX idx_room_type (RoomType),
    INDEX idx_available (IsAvailable),
    INDEX idx_price (Price)
);

-- Tạo bảng Bookings (Đặt phòng)
CREATE TABLE Bookings (
    BookingId INT AUTO_INCREMENT PRIMARY KEY,
    RoomId INT NOT NULL,
    UserId INT NOT NULL,
    CheckInDate DATE NOT NULL,
    CheckOutDate DATE NOT NULL,
    Status VARCHAR(20) NOT NULL DEFAULT 'Pending',
    TotalAmount DECIMAL(10,2) NOT NULL,
    BookingDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CustomerName VARCHAR(100) NOT NULL,
    CustomerEmail VARCHAR(100) NOT NULL,
    SpecialRequests TEXT,
    FOREIGN KEY (RoomId) REFERENCES Rooms(RoomId) ON DELETE CASCADE,
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    INDEX idx_room_id (RoomId),
    INDEX idx_user_id (UserId),
    INDEX idx_checkin_date (CheckInDate),
    INDEX idx_checkout_date (CheckOutDate),
    INDEX idx_status (Status),
    INDEX idx_booking_date (BookingDate)
);

-- Thêm dữ liệu mẫu cho bảng Rooms
INSERT INTO Rooms (RoomNumber, RoomType, Price, Description, IsAvailable, MaxOccupancy) VALUES
('101', 'Standard Single', 500000, 'Phòng đơn tiêu chuẩn với giường đơn, phòng tắm riêng, TV, WiFi miễn phí', TRUE, 1),
('102', 'Standard Double', 700000, 'Phòng đôi tiêu chuẩn với giường đôi, phòng tắm riêng, TV, WiFi miễn phí', TRUE, 2),
('103', 'Standard Twin', 750000, 'Phòng đôi với 2 giường đơn, phòng tắm riêng, TV, WiFi miễn phí', TRUE, 2),
('201', 'Deluxe Double', 1000000, 'Phòng deluxe với giường đôi lớn, ban công, minibar, TV màn hình phẳng', TRUE, 2),
('202', 'Deluxe Twin', 1050000, 'Phòng deluxe với 2 giường đơn, ban công, minibar, TV màn hình phẳng', TRUE, 2),
('203', 'Family Room', 1500000, 'Phòng gia đình rộng rãi với 1 giường đôi và 2 giường đơn, phù hợp cho 4 người', TRUE, 4),
('301', 'Suite', 2000000, 'Phòng suite cao cấp với phòng khách riêng, phòng ngủ, ban công lớn, jacuzzi', TRUE, 2),
('302', 'Presidential Suite', 3500000, 'Phòng tổng thống với 2 phòng ngủ, phòng khách lớn, bếp nhỏ, ban công view biển', TRUE, 4),
('401', 'Standard Single', 500000, 'Phòng đơn tiêu chuẩn với giường đơn, phòng tắm riêng, TV, WiFi miễn phí', TRUE, 1),
('402', 'Standard Double', 700000, 'Phòng đôi tiêu chuẩn với giường đôi, phòng tắm riêng, TV, WiFi miễn phí', TRUE, 2);

-- Thêm dữ liệu mẫu cho bảng Users (mật khẩu đã được hash)
-- Mật khẩu gốc: "123456" -> Hash SHA256
INSERT INTO Users (Name, Email, PasswordHash, Role) VALUES
('Admin User', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'Admin'),
('Nguyễn Văn An', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'Customer'),
('Trần Thị Bình', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'Customer'),
('Lê Văn Cường', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'Customer');

-- Thêm một số booking mẫu
INSERT INTO Bookings (RoomId, UserId, CheckInDate, CheckOutDate, Status, TotalAmount, CustomerName, CustomerEmail) VALUES
(1, 2, '2024-01-15', '2024-01-17', 'Confirmed', 1000000, 'Nguyễn Văn An', '<EMAIL>'),
(3, 3, '2024-01-20', '2024-01-23', 'Confirmed', 2250000, 'Trần Thị Bình', '<EMAIL>'),
(5, 4, '2024-02-01', '2024-02-03', 'Pending', 2100000, 'Lê Văn Cường', '<EMAIL>');

-- Tạo view để xem thông tin booking chi tiết
CREATE VIEW BookingDetails AS
SELECT 
    b.BookingId,
    b.CheckInDate,
    b.CheckOutDate,
    b.Status,
    b.TotalAmount,
    b.BookingDate,
    b.CustomerName,
    b.CustomerEmail,
    r.RoomNumber,
    r.RoomType,
    r.Price as RoomPrice,
    u.Name as UserName,
    u.Email as UserEmail,
    DATEDIFF(b.CheckOutDate, b.CheckInDate) as NumberOfNights
FROM Bookings b
INNER JOIN Rooms r ON b.RoomId = r.RoomId
INNER JOIN Users u ON b.UserId = u.UserId;

-- Tạo stored procedure để kiểm tra phòng trống
DELIMITER //
CREATE PROCEDURE CheckRoomAvailability(
    IN p_RoomId INT,
    IN p_CheckInDate DATE,
    IN p_CheckOutDate DATE
)
BEGIN
    SELECT COUNT(*) as ConflictCount
    FROM Bookings 
    WHERE RoomId = p_RoomId 
    AND Status IN ('Confirmed', 'Pending')
    AND (
        (CheckInDate <= p_CheckInDate AND CheckOutDate > p_CheckInDate) OR
        (CheckInDate < p_CheckOutDate AND CheckOutDate >= p_CheckOutDate) OR
        (CheckInDate >= p_CheckInDate AND CheckOutDate <= p_CheckOutDate)
    );
END //
DELIMITER ;

-- Tạo trigger để cập nhật trạng thái phòng khi có booking mới
DELIMITER //
CREATE TRIGGER UpdateRoomAvailability 
AFTER INSERT ON Bookings
FOR EACH ROW
BEGIN
    IF NEW.Status = 'Confirmed' THEN
        UPDATE Rooms 
        SET IsAvailable = FALSE 
        WHERE RoomId = NEW.RoomId;
    END IF;
END //
DELIMITER ;

-- Tạo trigger để cập nhật trạng thái phòng khi booking bị hủy
DELIMITER //
CREATE TRIGGER UpdateRoomAvailabilityOnCancel 
AFTER UPDATE ON Bookings
FOR EACH ROW
BEGIN
    IF OLD.Status != 'Cancelled' AND NEW.Status = 'Cancelled' THEN
        -- Kiểm tra xem có booking nào khác cho phòng này không
        IF NOT EXISTS (
            SELECT 1 FROM Bookings 
            WHERE RoomId = NEW.RoomId 
            AND Status IN ('Confirmed', 'Pending')
            AND BookingId != NEW.BookingId
        ) THEN
            UPDATE Rooms 
            SET IsAvailable = TRUE 
            WHERE RoomId = NEW.RoomId;
        END IF;
    END IF;
END //
DELIMITER ;

-- Hiển thị thông tin các bảng đã tạo
SHOW TABLES;

-- Hiển thị dữ liệu mẫu
SELECT 'Users Table:' as Info;
SELECT * FROM Users;

SELECT 'Rooms Table:' as Info;
SELECT * FROM Rooms;

SELECT 'Bookings Table:' as Info;
SELECT * FROM Bookings;

SELECT 'BookingDetails View:' as Info;
SELECT * FROM BookingDetails;
