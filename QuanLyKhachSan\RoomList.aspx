<%@ Page Title="Danh sách phòng" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true"
    CodeBehind="RoomList.aspx.cs" Inherits="QuanLyKhachSan.RoomList" %>

    <asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h2>Danh sách phòng khách sạn</h2>
                    <hr />
                </div>
            </div>

            <!-- Search and Filter Section -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>Tìm kiếm phòng</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <label>Ngày nhận phòng:</label>
                                    <asp:TextBox ID="txtCheckIn" runat="server" CssClass="form-control" TextMode="Date">
                                    </asp:TextBox>
                                </div>
                                <div class="col-md-3">
                                    <label>Ngày trả phòng:</label>
                                    <asp:TextBox ID="txtCheckOut" runat="server" CssClass="form-control"
                                        TextMode="Date"></asp:TextBox>
                                </div>
                                <div class="col-md-3">
                                    <label>Loại phòng:</label>
                                    <asp:DropDownList ID="ddlRoomType" runat="server" CssClass="form-control">
                                        <asp:ListItem Value="" Text="Tất cả"></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                                <div class="col-md-3">
                                    <label>&nbsp;</label><br />
                                    <asp:Button ID="btnSearch" runat="server" Text="Tìm kiếm" CssClass="btn btn-primary"
                                        OnClick="btnSearch_Click" />
                                    <asp:Button ID="btnReset" runat="server" Text="Đặt lại" CssClass="btn btn-secondary"
                                        OnClick="btnReset_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Panel -->
            <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
                <asp:Label ID="lblMessage" runat="server"></asp:Label>
            </asp:Panel>

            <!-- Rooms Display -->
            <div class="row">
                <asp:Repeater ID="rptRooms" runat="server" OnItemCommand="rptRooms_ItemCommand">
                    <ItemTemplate>
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="card-title">Phòng <%# Eval("RoomNumber") %>
                                    </h5>
                                    <span class="badge <%# (bool)Eval(" IsAvailable") ? "badge-success" : "badge-danger"
                                        %>">
                                        <%# (bool)Eval("IsAvailable") ? "Có sẵn" : "Đã đặt" %>
                                    </span>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        <strong>Loại phòng:</strong>
                                        <%# Eval("RoomType") %><br />
                                            <strong>Giá:</strong>
                                            <%# String.Format("{0:N0} VNĐ/đêm", Eval("Price")) %><br />
                                                <strong>Sức chứa:</strong>
                                                <%# Eval("MaxOccupancy") %> người<br />
                                                    <strong>Mô tả:</strong>
                                                    <%# Eval("Description") %>
                                    </p>
                                </div>
                                <div class="card-footer">
                                    <asp:Button ID="btnViewDetails" runat="server" Text="Xem chi tiết"
                                        CssClass="btn btn-info btn-sm" CommandName="ViewDetails"
                                        CommandArgument='<%# Eval("RoomId") %>' />

                                    <asp:Button ID="btnBookRoom" runat="server" Text="Đặt phòng"
                                        CssClass="btn btn-primary btn-sm" CommandName="BookRoom"
                                        CommandArgument='<%# Eval("RoomId") %>'
                                        Visible='<%# (bool)Eval("IsAvailable") %>' />
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                </asp:Repeater>
            </div>

            <!-- No Rooms Message -->
            <asp:Panel ID="pnlNoRooms" runat="server" Visible="false" CssClass="text-center">
                <div class="alert alert-info">
                    <h4>Không tìm thấy phòng nào</h4>
                    <p>Vui lòng thử lại với các tiêu chí tìm kiếm khác.</p>
                </div>
            </asp:Panel>
        </div>
    </asp:Content>