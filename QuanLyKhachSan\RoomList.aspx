<%@ Page Title="Danh sách phòng" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true"
    CodeBehind="RoomList.aspx.cs" Inherits="QuanLyKhachSan.RoomList" %>

    <asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h1 class="mb-3" style="color: var(--text-primary); font-weight: 600;">
                        <i class="fas fa-bed me-3" style="color: var(--accent-yellow);"></i>
                        Danh sách phòng khách sạn
                    </h1>
                    <p class="lead" style="color: var(--text-secondary);">
                        Khám phá những căn phòng tuyệt vời với tiện nghi hiện đại
                    </p>
                </div>
            </div>

            <!-- Search and Filter Section -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="card fade-in">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-search me-2" style="color: var(--accent-cyan);"></i>
                                Tìm kiếm phòng
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <div class="col-lg-3 col-md-6">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-alt me-2" style="color: var(--accent-yellow);"></i>
                                        Ngày nhận phòng:
                                    </label>
                                    <asp:TextBox ID="txtCheckIn" runat="server" CssClass="form-control" TextMode="Date">
                                    </asp:TextBox>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-check me-2" style="color: var(--accent-teal);"></i>
                                        Ngày trả phòng:
                                    </label>
                                    <asp:TextBox ID="txtCheckOut" runat="server" CssClass="form-control"
                                        TextMode="Date"></asp:TextBox>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="form-label">
                                        <i class="fas fa-bed me-2" style="color: var(--accent-purple);"></i>
                                        Loại phòng:
                                    </label>
                                    <asp:DropDownList ID="ddlRoomType" runat="server" CssClass="form-control">
                                        <asp:ListItem Value="" Text="Tất cả loại phòng"></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <asp:Button ID="btnSearch" runat="server" Text="Tìm kiếm"
                                            CssClass="btn btn-primary" OnClick="btnSearch_Click" />
                                        <asp:Button ID="btnReset" runat="server" Text="Đặt lại"
                                            CssClass="btn btn-secondary" OnClick="btnReset_Click" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Panel -->
            <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
                <asp:Label ID="lblMessage" runat="server"></asp:Label>
            </asp:Panel>

            <!-- Rooms Display -->
            <div class="row g-4">
                <asp:Repeater ID="rptRooms" runat="server" OnItemCommand="rptRooms_ItemCommand">
                    <ItemTemplate>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 fade-in">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0" style="color: var(--text-primary);">
                                        <i class="fas fa-door-open me-2" style="color: var(--accent-yellow);"></i>
                                        Phòng <%# Eval("RoomNumber") %>
                                    </h5>
                                    <span class="badge <%# (bool)Eval(" IsAvailable") ? "badge-success" : "badge-danger"
                                        %>">
                                        <%# (bool)Eval("IsAvailable") ? "Có sẵn" : "Đã đặt" %>
                                    </span>
                                </div>
                                <div class="card-body p-4">
                                    <div class="room-info mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-bed me-2" style="color: var(--accent-cyan);"></i>
                                            <span style="color: var(--text-secondary);">Loại phòng:</span>
                                            <strong class="ms-2" style="color: var(--text-primary);">
                                                <%# Eval("RoomType") %>
                                            </strong>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-money-bill-wave me-2"
                                                style="color: var(--accent-yellow);"></i>
                                            <span style="color: var(--text-secondary);">Giá:</span>
                                            <strong class="ms-2" style="color: var(--accent-orange);">
                                                <%# String.Format("{0:N0} VNĐ/đêm", Eval("Price")) %>
                                            </strong>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-users me-2" style="color: var(--accent-purple);"></i>
                                            <span style="color: var(--text-secondary);">Sức chứa:</span>
                                            <strong class="ms-2" style="color: var(--text-primary);">
                                                <%# Eval("MaxOccupancy") %> người
                                            </strong>
                                        </div>
                                    </div>
                                    <div class="room-description">
                                        <p class="card-text" style="color: var(--text-secondary); font-size: 0.9rem;">
                                            <i class="fas fa-info-circle me-2" style="color: var(--accent-teal);"></i>
                                            <%# Eval("Description") %>
                                        </p>
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-4 pt-0">
                                    <div class="d-grid gap-2">
                                        <asp:Button ID="btnViewDetails" runat="server" Text="Xem chi tiết"
                                            CssClass="btn btn-info" CommandName="ViewDetails"
                                            CommandArgument='<%# Eval("RoomId") %>' />
                                        <asp:Button ID="btnBookRoom" runat="server" Text="Đặt phòng ngay"
                                            CssClass="btn btn-primary" CommandName="BookRoom"
                                            CommandArgument='<%# Eval("RoomId") %>'
                                            Visible='<%# (bool)Eval("IsAvailable") %>' />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                </asp:Repeater>
            </div>

            <!-- No Rooms Message -->
            <asp:Panel ID="pnlNoRooms" runat="server" Visible="false" CssClass="text-center">
                <div class="alert alert-info">
                    <h4>Không tìm thấy phòng nào</h4>
                    <p>Vui lòng thử lại với các tiêu chí tìm kiếm khác.</p>
                </div>
            </asp:Panel>
        </div>
    </asp:Content>