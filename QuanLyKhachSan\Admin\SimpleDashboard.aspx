<%@ Page Title="Admin Dashboard" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true"
    CodeBehind="SimpleDashboard.aspx.cs" Inherits="QuanLyKhachSan.SimpleDashboard" %>

    <asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
        <div class="container-fluid mt-4">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                                <i class="fas fa-tachometer-alt me-3" style="color: var(--accent-blue);"></i>
                                Admin Dashboard
                            </h1>
                            <p class="lead" style="color: var(--text-secondary);">
                                Tổng quan hệ thống quản lý khách sạn
                            </p>
                        </div>
                        <div>
                            <span class="badge bg-success fs-6 px-3 py-2">
                                <i class="fas fa-user-shield me-2"></i>
                                <asp:Label ID="lblAdminName" runat="server"></asp:Label>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
                <asp:Label ID="lblMessage" runat="server"></asp:Label>
            </asp:Panel>

            <!-- Statistics Cards -->
            <div class="row mb-5">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-number" style="color: var(--accent-blue);">
                            <i class="fas fa-bed me-2"></i>
                            <asp:Label ID="lblTotalRooms" runat="server" Text="0"></asp:Label>
                        </div>
                        <p style="color: var(--text-secondary); font-weight: 500;">Tổng số phòng</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-number" style="color: var(--accent-mint);">
                            <i class="fas fa-calendar-check me-2"></i>
                            <asp:Label ID="lblTotalBookings" runat="server" Text="0"></asp:Label>
                        </div>
                        <p style="color: var(--text-secondary); font-weight: 500;">Tổng đặt phòng</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-number" style="color: var(--accent-orange);">
                            <i class="fas fa-users me-2"></i>
                            <asp:Label ID="lblTotalUsers" runat="server" Text="0"></asp:Label>
                        </div>
                        <p style="color: var(--text-secondary); font-weight: 500;">Tổng khách hàng</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-number" style="color: var(--accent-yellow);">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            <asp:Label ID="lblTotalRevenue" runat="server" Text="0"></asp:Label>
                        </div>
                        <p style="color: var(--text-secondary); font-weight: 500;">Doanh thu (VNĐ)</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2" style="color: var(--accent-blue);"></i>
                                Thao tác nhanh
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-lg-3 col-md-6">
                                    <a href="ManageRooms.aspx" class="btn btn-primary w-100 py-3">
                                        <i class="fas fa-bed me-2"></i>Quản lý phòng
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <a href="ManageBookings.aspx" class="btn btn-success w-100 py-3">
                                        <i class="fas fa-calendar-check me-2"></i>Quản lý đặt phòng
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <a href="ManageUsers.aspx" class="btn btn-info w-100 py-3">
                                        <i class="fas fa-users me-2"></i>Quản lý khách hàng
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <a href="../Default.aspx" class="btn btn-secondary w-100 py-3">
                                        <i class="fas fa-home me-2"></i>Về trang chủ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2" style="color: var(--accent-orange);"></i>
                                Thông tin hệ thống
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Server Time:</span>
                                    <strong>
                                        <%= DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") %>
                                    </strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Admin User:</span>
                                    <strong>
                                        <asp:Label ID="lblAdminName" runat="server"></asp:Label>
                                    </strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Session ID:</span>
                                    <strong>
                                        <%= Session.SessionID %>
                                    </strong>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2" style="color: var(--accent-purple);"></i>
                                Trạng thái
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                Hệ thống đang hoạt động bình thường
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Database kết nối thành công
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </asp:Content>