using System;
using System.Security.Cryptography;
using System.Text;
using QuanLyKhachSan.DAL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan.BLL
{
    public class UserManager
    {
        private UserDAO userDAO;

        public UserManager()
        {
            userDAO = new UserDAO();
        }

        public bool RegisterUser(string name, string email, string password)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(email) || string.IsNullOrWhiteSpace(password))
                {
                    return false;
                }

                if (password.Length < 6)
                {
                    return false;
                }

                // Check if email already exists
                if (userDAO.EmailExists(email))
                {
                    return false;
                }

                // Create new user
                var user = new User
                {
                    Name = name.Trim(),
                    Email = email.Trim().ToLower(),
                    PasswordHash = HashPassword(password),
                    Role = "Customer"
                };

                return userDAO.CreateUser(user);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in RegisterUser: {ex.Message}");
                return false;
            }
        }

        public User AuthenticateUser(string email, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email) || string.IsNullOrWhiteSpace(password))
                {
                    return null;
                }

                var user = userDAO.GetUserByEmail(email.Trim().ToLower());
                if (user != null && VerifyPassword(password, user.PasswordHash))
                {
                    return user;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AuthenticateUser: {ex.Message}");
                return null;
            }
        }

        public User GetUserById(int userId)
        {
            try
            {
                return userDAO.GetUserById(userId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetUserById: {ex.Message}");
                return null;
            }
        }

        public bool IsEmailAvailable(string email)
        {
            try
            {
                return !userDAO.EmailExists(email.Trim().ToLower());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in IsEmailAvailable: {ex.Message}");
                return false;
            }
        }

        public bool UpdateUser(User user)
        {
            try
            {
                if (user == null || string.IsNullOrWhiteSpace(user.Name) || string.IsNullOrWhiteSpace(user.Email))
                {
                    return false;
                }

                user.Name = user.Name.Trim();
                user.Email = user.Email.Trim().ToLower();

                return userDAO.UpdateUser(user);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateUser: {ex.Message}");
                return false;
            }
        }

        private string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                // ComputeHash - returns byte array
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password + "salt"));

                // Convert byte array to a string
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        private bool VerifyPassword(string password, string hash)
        {
            string hashOfInput = HashPassword(password);
            return string.Equals(hashOfInput, hash, StringComparison.OrdinalIgnoreCase);
        }

        public bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        public string ValidateRegistrationData(string name, string email, string password, string confirmPassword)
        {
            if (string.IsNullOrWhiteSpace(name))
                return "Tên không được để trống.";

            if (string.IsNullOrWhiteSpace(email))
                return "Email không được để trống.";

            if (!IsValidEmail(email))
                return "Email không hợp lệ.";

            if (string.IsNullOrWhiteSpace(password))
                return "Mật khẩu không được để trống.";

            if (password.Length < 6)
                return "Mật khẩu phải có ít nhất 6 ký tự.";

            if (password != confirmPassword)
                return "Mật khẩu xác nhận không khớp.";

            if (!IsEmailAvailable(email))
                return "Email này đã được sử dụng.";

            return string.Empty; // No errors
        }
    }
}
