<%@ Page Title="Hồ sơ của tôi" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MyProfile.aspx.cs" Inherits="QuanLyKhachSan.MyProfile" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1 class="mb-3" style="color: var(--text-primary); font-weight: 600;">
                    <i class="fas fa-user-circle me-3" style="color: var(--accent-blue);"></i>
                    Hồ sơ của tôi
                </h1>
                <p class="lead" style="color: var(--text-secondary);">
                    Quản lý thông tin cá nhân và xem lịch sử đặt phòng
                </p>
            </div>
        </div>

        <!-- Alert Messages -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
            <asp:Label ID="lblMessage" runat="server"></asp:Label>
        </asp:Panel>

        <div class="row">
            <!-- Profile Information -->
            <div class="col-lg-4 mb-4">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2" style="color: var(--accent-mint);"></i>
                            Thông tin cá nhân
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="mb-3">
                                <i class="fas fa-user-circle fa-5x" style="color: var(--accent-blue);"></i>
                            </div>
                            <h4 style="color: var(--text-primary);">
                                <asp:Label ID="lblUserName" runat="server"></asp:Label>
                            </h4>
                            <p style="color: var(--text-secondary);">
                                <i class="fas fa-envelope me-2"></i>
                                <asp:Label ID="lblUserEmail" runat="server"></asp:Label>
                            </p>
                            <p style="color: var(--text-muted);">
                                <i class="fas fa-calendar me-2"></i>
                                Thành viên từ: <asp:Label ID="lblMemberSince" runat="server"></asp:Label>
                            </p>
                        </div>
                        
                        <div class="d-grid">
                            <asp:Button ID="btnEditProfile" runat="server" Text="Chỉnh sửa thông tin" 
                                CssClass="btn btn-outline-primary" OnClick="btnEditProfile_Click" />
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card mt-4 fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2" style="color: var(--accent-orange);"></i>
                            Thống kê
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h3 style="color: var(--accent-blue);">
                                    <asp:Label ID="lblTotalBookings" runat="server" Text="0"></asp:Label>
                                </h3>
                                <p style="color: var(--text-secondary); font-size: 0.9rem;">Tổng đặt phòng</p>
                            </div>
                            <div class="col-6">
                                <h3 style="color: var(--accent-mint);">
                                    <asp:Label ID="lblTotalSpent" runat="server" Text="0"></asp:Label>
                                </h3>
                                <p style="color: var(--text-secondary); font-size: 0.9rem;">Tổng chi tiêu</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking History -->
            <div class="col-lg-8">
                <div class="card fade-in">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2" style="color: var(--accent-yellow);"></i>
                            Lịch sử đặt phòng
                        </h5>
                        <div>
                            <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select form-select-sm" 
                                AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                <asp:ListItem Value="" Text="Tất cả trạng thái"></asp:ListItem>
                                <asp:ListItem Value="Confirmed" Text="Đã xác nhận"></asp:ListItem>
                                <asp:ListItem Value="Pending" Text="Chờ xác nhận"></asp:ListItem>
                                <asp:ListItem Value="Completed" Text="Hoàn thành"></asp:ListItem>
                                <asp:ListItem Value="Cancelled" Text="Đã hủy"></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptBookings" runat="server" OnItemCommand="rptBookings_ItemCommand">
                            <ItemTemplate>
                                <div class="card mb-3 border-start border-4" style="border-left-color: <%# GetStatusColor(Eval("Status").ToString()) %> !important;">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="mb-1" style="color: var(--text-primary);">
                                                        <i class="fas fa-bed me-2" style="color: var(--accent-blue);"></i>
                                                        Phòng <%# Eval("RoomNumber") %> - <%# Eval("RoomType") %>
                                                    </h6>
                                                    <span class="badge <%# GetStatusBadgeClass(Eval("Status").ToString()) %>">
                                                        <%# GetStatusText(Eval("Status").ToString()) %>
                                                    </span>
                                                </div>
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <p class="mb-1" style="color: var(--text-secondary); font-size: 0.9rem;">
                                                            <i class="fas fa-calendar-alt me-2" style="color: var(--accent-mint);"></i>
                                                            Nhận phòng: <%# Eval("CheckInDate", "{0:dd/MM/yyyy}") %>
                                                        </p>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <p class="mb-1" style="color: var(--text-secondary); font-size: 0.9rem;">
                                                            <i class="fas fa-calendar-check me-2" style="color: var(--accent-orange);"></i>
                                                            Trả phòng: <%# Eval("CheckOutDate", "{0:dd/MM/yyyy}") %>
                                                        </p>
                                                    </div>
                                                </div>
                                                <p class="mb-1" style="color: var(--text-secondary); font-size: 0.9rem;">
                                                    <i class="fas fa-clock me-2" style="color: var(--accent-yellow);"></i>
                                                    Đặt ngày: <%# Eval("BookingDate", "{0:dd/MM/yyyy HH:mm}") %>
                                                </p>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <h5 class="mb-2" style="color: var(--accent-orange);">
                                                    <%# Eval("TotalAmount", "{0:N0}") %> VNĐ
                                                </h5>
                                                <div class="btn-group-vertical w-100" role="group">
                                                    <asp:Button ID="btnViewDetails" runat="server" Text="Chi tiết" 
                                                        CssClass="btn btn-outline-primary btn-sm mb-1" 
                                                        CommandName="ViewDetails" CommandArgument='<%# Eval("BookingId") %>' />
                                                    <asp:Button ID="btnCancelBooking" runat="server" Text="Hủy đặt phòng" 
                                                        CssClass="btn btn-outline-danger btn-sm" 
                                                        CommandName="CancelBooking" CommandArgument='<%# Eval("BookingId") %>'
                                                        Visible='<%# CanCancelBooking(Eval("Status").ToString(), (DateTime)Eval("CheckInDate")) %>'
                                                        OnClientClick="return confirm('Bạn có chắc chắn muốn hủy đặt phòng này?');" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>

                        <asp:Panel ID="pnlNoBookings" runat="server" Visible="false" CssClass="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x mb-3" style="color: var(--text-muted);"></i>
                            <h5 style="color: var(--text-secondary);">Chưa có đặt phòng nào</h5>
                            <p style="color: var(--text-muted);">Hãy khám phá các phòng tuyệt vời của chúng tôi!</p>
                            <a href="RoomList.aspx" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Tìm phòng ngay
                            </a>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
