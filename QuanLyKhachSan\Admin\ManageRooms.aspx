<%@ Page Language="C#" AutoEventWireup="true" %>
    <%@ Import Namespace="QuanLyKhachSan.BLL" %>
        <%@ Import Namespace="QuanLyKhachSan.Models" %>
            <%@ Import Namespace="System.Linq" %>

                <!DOCTYPE html>
                <html>

                <head>
                    <title>Quản lý phòng</title>
                    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
                    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
                        rel="stylesheet" />
                </head>

                <body>
                    <script runat="server">
        private RoomManager roomManager;
        private List < Room > rooms;

        protected void Page_Load(object sender, EventArgs e)
                        {
                            // Check if user is logged in and is admin
                            if (Session["UserId"] == null) {
                                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                                return;
                            }

                            if (Session["UserRole"]?.ToString() != "Admin") {
                                Response.Redirect("../Default.aspx");
                                return;
                            }

                            roomManager = new RoomManager();
                            LoadRooms();
                        }

        private void LoadRooms()
                        {
                            try {
                                rooms = roomManager.GetAllRooms();
                            }
                            catch (Exception ex)
                            {
                                rooms = new List < Room > ();
                                System.Diagnostics.Debug.WriteLine($"Error loading rooms: {ex.Message}");
                            }
                        }

        protected string GetStatusBadgeClass(bool isAvailable)
                        {
                            return isAvailable ? "bg-success" : "bg-danger";
                        }

        protected string GetStatusText(bool isAvailable)
                        {
                            return isAvailable ? "Có sẵn" : "Đã đặt";
                        }
                    </script>

                    <div class="container-fluid mt-4">
                        <!-- Navigation -->
                        <nav class="navbar navbar-expand-lg navbar-dark mb-4">
                            <div class="container-fluid">
                                <a class="navbar-brand" href="../Default.aspx">
                                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                                </a>
                                <div class="navbar-nav ms-auto">
                                    <a class="nav-link" href="SimpleDashboard.aspx">
                                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                    </a>
                                    <a class="nav-link" href="../Default.aspx">
                                        <i class="fas fa-home me-1"></i>Trang chủ
                                    </a>
                                </div>
                            </div>
                        </nav>

                        <!-- Page Header -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                                            <i class="fas fa-bed me-3" style="color: var(--accent-blue);"></i>
                                            Quản lý phòng
                                        </h1>
                                        <p class="lead" style="color: var(--text-secondary);">
                                            Quản lý thông tin và trạng thái các phòng
                                        </p>
                                    </div>
                                    <div>
                                        <a href="SimpleDashboard.aspx" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Quay lại Dashboard
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Cards -->
                        <div class="row mb-4">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="stats-card">
                                    <div class="stats-number" style="color: var(--accent-blue);">
                                        <i class="fas fa-bed me-2"></i>
                                        <%= rooms?.Count ?? 0 %>
                                    </div>
                                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng số phòng</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="stats-card">
                                    <div class="stats-number" style="color: var(--accent-mint);">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <%= rooms?.Where(r=> r.IsAvailable).Count() ?? 0 %>
                                    </div>
                                    <p style="color: var(--text-secondary); font-weight: 500;">Phòng trống</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="stats-card">
                                    <div class="stats-number" style="color: var(--accent-orange);">
                                        <i class="fas fa-times-circle me-2"></i>
                                        <%= rooms?.Where(r=> !r.IsAvailable).Count() ?? 0 %>
                                    </div>
                                    <p style="color: var(--text-secondary); font-weight: 500;">Phòng đã đặt</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="stats-card">
                                    <div class="stats-number" style="color: var(--accent-yellow);">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        <%= rooms?.Average(r=> r.Price).ToString("N0") ?? "0" %>
                                    </div>
                                    <p style="color: var(--text-secondary); font-weight: 500;">Giá TB (VNĐ)</p>
                                </div>
                            </div>
                        </div>

                        <!-- Rooms List -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="fas fa-list me-2" style="color: var(--accent-blue);"></i>
                                            Danh sách phòng
                                        </h5>
                                        <div>
                                            <button class="btn btn-success"
                                                onclick="alert('Chức năng thêm phòng sẽ được phát triển')">
                                                <i class="fas fa-plus me-2"></i>Thêm phòng
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <% if (rooms !=null && rooms.Any()) { %>
                                            <div class="row g-4">
                                                <% foreach (var room in rooms) { %>
                                                    <div class="col-lg-4 col-md-6">
                                                        <div class="card h-100 hover-lift">
                                                            <div
                                                                class="card-header d-flex justify-content-between align-items-center">
                                                                <h6 class="mb-0" style="color: var(--text-primary);">
                                                                    <i class="fas fa-door-open me-2"
                                                                        style="color: var(--accent-blue);"></i>
                                                                    Phòng <%= room.RoomNumber %>
                                                                </h6>
                                                                <span
                                                                    class="badge <%= GetStatusBadgeClass(room.IsAvailable) %>">
                                                                    <%= GetStatusText(room.IsAvailable) %>
                                                                </span>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="mb-3">
                                                                    <div class="d-flex align-items-center mb-2">
                                                                        <i class="fas fa-bed me-2"
                                                                            style="color: var(--accent-mint);"></i>
                                                                        <span
                                                                            style="color: var(--text-secondary);">Loại:</span>
                                                                        <strong class="ms-2"
                                                                            style="color: var(--text-primary);">
                                                                            <%= room.RoomType %>
                                                                        </strong>
                                                                    </div>
                                                                    <div class="d-flex align-items-center mb-2">
                                                                        <i class="fas fa-money-bill-wave me-2"
                                                                            style="color: var(--accent-yellow);"></i>
                                                                        <span
                                                                            style="color: var(--text-secondary);">Giá:</span>
                                                                        <strong class="ms-2"
                                                                            style="color: var(--accent-orange);">
                                                                            <%= room.Price.ToString("N0") %> VNĐ/đêm
                                                                        </strong>
                                                                    </div>
                                                                    <div class="d-flex align-items-center mb-2">
                                                                        <i class="fas fa-users me-2"
                                                                            style="color: var(--accent-purple);"></i>
                                                                        <span style="color: var(--text-secondary);">Sức
                                                                            chứa:</span>
                                                                        <strong class="ms-2"
                                                                            style="color: var(--text-primary);">
                                                                            <%= room.MaxOccupancy %> người
                                                                        </strong>
                                                                    </div>
                                                                </div>
                                                                <div class="room-description">
                                                                    <p class="card-text"
                                                                        style="color: var(--text-secondary); font-size: 0.9rem;">
                                                                        <i class="fas fa-info-circle me-2"
                                                                            style="color: var(--accent-blue);"></i>
                                                                        <%= room.Description %>
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            <div class="card-footer bg-transparent border-0 p-3">
                                                                <div class="d-grid gap-2">
                                                                    <button class="btn btn-outline-primary btn-sm"
                                                                        onclick="alert('Chức năng chỉnh sửa phòng <%= room.RoomNumber %> sẽ được phát triển')">
                                                                        <i class="fas fa-edit me-2"></i>Chỉnh sửa
                                                                    </button>
                                                                    <button
                                                                        class="btn btn-outline-<%= room.IsAvailable ? "
                                                                        warning" : "success" %> btn-sm"
                                                                        onclick="alert('Chức năng thay đổi trạng thái
                                                                        phòng <%= room.RoomNumber %> sẽ được phát
                                                                            triển')">
                                                                            <i class="fas fa-<%= room.IsAvailable ? "
                                                                                lock" : "unlock" %> me-2"></i>
                                                                            <%= room.IsAvailable ? "Đánh dấu đã đặt"
                                                                                : "Đánh dấu trống" %>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <% } %>
                                            </div>
                                            <% } else { %>
                                                <div class="text-center py-5">
                                                    <i class="fas fa-bed fa-4x mb-4"
                                                        style="color: var(--text-muted);"></i>
                                                    <h4 style="color: var(--text-secondary);">Không có phòng nào</h4>
                                                    <p style="color: var(--text-muted);">Hệ thống chưa có dữ liệu phòng
                                                        hoặc có lỗi kết nối database.</p>
                                                </div>
                                                <% } %>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-bolt me-2" style="color: var(--accent-orange);"></i>
                                            Thao tác nhanh
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-lg-3 col-md-6">
                                                <a href="ManageBookings.aspx" class="btn btn-success w-100">
                                                    <i class="fas fa-calendar-check me-2"></i>Quản lý đặt phòng
                                                </a>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <a href="ManageUsers.aspx" class="btn btn-info w-100">
                                                    <i class="fas fa-users me-2"></i>Quản lý khách hàng
                                                </a>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <a href="../RoomList.aspx" class="btn btn-outline-primary w-100">
                                                    <i class="fas fa-eye me-2"></i>Xem trang khách hàng
                                                </a>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <a href="SimpleDashboard.aspx" class="btn btn-outline-secondary w-100">
                                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script src="../Scripts/bootstrap.bundle.min.js"></script>
                </body>

                </html>