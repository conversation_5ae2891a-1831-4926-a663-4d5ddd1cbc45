using System;
using System.Web.UI;
using QuanLyKhachSan.BLL;

namespace QuanLyKhachSan
{
    public partial class Login : Page
    {
        private UserManager userManager;

        protected void Page_Load(object sender, EventArgs e)
        {
            userManager = new UserManager();

            // If user is already logged in, redirect to home page
            if (Session["UserId"] != null)
            {
                Response.Redirect("Default.aspx");
            }
        }

        protected void btnLogin_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                try
                {
                    string email = txtEmail.Text.Trim();
                    string password = txtPassword.Text;

                    // Attempt to authenticate user
                    var user = userManager.AuthenticateUser(email, password);

                    if (user != null)
                    {
                        // Store user information in session
                        Session["UserId"] = user.UserId;
                        Session["UserName"] = user.Name;
                        Session["UserEmail"] = user.Email;
                        Session["UserRole"] = user.Role;

                        // Redirect to the page user was trying to access, or default page
                        string returnUrl = Request.QueryString["ReturnUrl"];
                        if (!string.IsNullOrEmpty(returnUrl))
                        {
                            Response.Redirect(returnUrl);
                        }
                        else
                        {
                            Response.Redirect("RoomList.aspx");
                        }
                    }
                    else
                    {
                        ShowMessage("Email hoặc mật khẩu không đúng. Vui lòng thử lại.", "alert-danger");
                        txtPassword.Text = ""; // Clear password field
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Login error: {ex.Message}");
                    ShowMessage("Có lỗi xảy ra trong quá trình đăng nhập. Vui lòng thử lại.", "alert-danger");
                }
            }
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }
    }
}
