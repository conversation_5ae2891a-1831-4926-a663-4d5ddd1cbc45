<%@ Page Language="C#" AutoEventWireup="true" %>

    <!DOCTYPE html>
    <html>

    <head>
        <title>Working Test</title>
        <link href="Content/bootstrap.min.css" rel="stylesheet" />
        <link href="Content/custom-light-theme.css" rel="stylesheet" />
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    </head>

    <body>
        <div class="container mt-5">
            <div class="card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-check-circle me-2" style="color: var(--accent-mint);"></i>
                        Working Test - ASP.NET Hoạt động!
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <strong>Thành công!</strong> Trang ASP.NET này đang hoạt động bình thường.
                    </div>

                    <h4>Thông tin Server:</h4>
                    <ul class="list-group mb-4">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Server Time:</span>
                            <strong>
                                <%= DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") %>
                            </strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Session ID:</span>
                            <strong>
                                <%= Session.SessionID %>
                            </strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>User ID:</span>
                            <strong>
                                <%= Session["UserId"] ?? "Not logged in" %>
                            </strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>User Name:</span>
                            <strong>
                                <%= Session["UserName"] ?? "N/A" %>
                            </strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>User Role:</span>
                            <strong>
                                <%= Session["UserRole"] ?? "N/A" %>
                            </strong>
                        </li>
                    </ul>

                    <h4>Test Navigation:</h4>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6>Basic Pages</h6>
                                    <div class="d-grid gap-2">
                                        <a href="Default.aspx" class="btn btn-primary btn-sm">Default.aspx</a>
                                        <a href="RoomList.aspx" class="btn btn-info btn-sm">RoomList.aspx</a>
                                        <a href="Login.aspx" class="btn btn-warning btn-sm">Login.aspx</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6>Test Pages</h6>
                                    <div class="d-grid gap-2">
                                        <a href="Test.aspx" class="btn btn-success btn-sm">Test.aspx</a>
                                        <a href="QuickTest.aspx" class="btn btn-secondary btn-sm">QuickTest.aspx</a>
                                        <a href="SimpleTest.html" class="btn btn-light btn-sm">SimpleTest.html</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6>Admin Pages</h6>
                                    <div class="d-grid gap-2">
                                        <a href="Admin/WorkingDashboard.aspx"
                                            class="btn btn-success btn-sm">WorkingDashboard.aspx ✅</a>
                                        <a href="Admin/ManageRoomsAdvanced.aspx"
                                            class="btn btn-success btn-sm">ManageRoomsAdvanced.aspx ✅</a>
                                        <a href="Admin/TestRoomEdit.aspx" class="btn btn-info btn-sm">TestRoomEdit.aspx
                                            🧪</a>
                                        <a href="Admin/ManageRooms.aspx" class="btn btn-warning btn-sm">ManageRooms.aspx
                                            ⚠️</a>
                                        <a href="Admin/ManageBookingsAdvanced.aspx"
                                            class="btn btn-success btn-sm">ManageBookingsAdvanced.aspx ✅</a>
                                        <a href="Admin/ManageBookings.aspx"
                                            class="btn btn-warning btn-sm">ManageBookings.aspx ⚠️</a>
                                        <a href="Admin/ManageUsers.aspx" class="btn btn-success btn-sm">ManageUsers.aspx
                                            ✅</a>
                                        <a href="Admin/SimpleAdmin.aspx" class="btn btn-warning btn-sm">SimpleAdmin.aspx
                                            ⚠️</a>
                                        <a href="Admin/SimpleDashboard.aspx"
                                            class="btn btn-danger btn-sm">SimpleDashboard.aspx ❌</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <h4>Hướng dẫn test:</h4>
                    <ol>
                        <li><strong>Đăng nhập admin:</strong> Login.aspx với email: <EMAIL>, password: 123456
                        </li>
                        <li><strong>Test admin page:</strong> Sau khi đăng nhập, click vào "SimpleAdmin.aspx ✅"</li>
                        <li><strong>Kiểm tra navbar:</strong> Sau khi đăng nhập admin, sẽ thấy menu "Admin" trên navbar
                        </li>
                    </ol>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Lưu ý:</strong> Chỉ trang SimpleAdmin.aspx được đảm bảo hoạt động. Các trang khác có thể
                        bị lỗi Parser Error.
                    </div>
                </div>
            </div>
        </div>
    </body>

    </html>