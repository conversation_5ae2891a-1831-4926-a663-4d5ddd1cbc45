using System;
using System.Collections.Generic;
using QuanLyKhachSan.DAL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan.BLL
{
    public class BookingManager
    {
        private BookingDAO bookingDAO;
        private RoomManager roomManager;
        private UserManager userManager;

        public BookingManager()
        {
            bookingDAO = new BookingDAO();
            roomManager = new RoomManager();
            userManager = new UserManager();
        }

        public bool CreateBooking(int roomId, int userId, DateTime checkIn, DateTime checkOut)
        {
            try
            {
                // Validate dates
                string dateValidation = roomManager.ValidateBookingDates(checkIn, checkOut);
                if (!string.IsNullOrEmpty(dateValidation))
                {
                    return false;
                }

                // Check room availability
                if (!roomManager.IsRoomAvailable(roomId, checkIn, checkOut))
                {
                    return false;
                }

                // Get room and user information
                var room = roomManager.GetRoomById(roomId);
                var user = userManager.GetUserById(userId);

                if (room == null || user == null)
                {
                    return false;
                }

                // Calculate total amount
                decimal totalAmount = roomManager.CalculateRoomPrice(roomId, checkIn, checkOut);

                // Create booking
                var booking = new Booking
                {
                    RoomId = roomId,
                    UserId = userId,
                    CheckInDate = checkIn,
                    CheckOutDate = checkOut,
                    Status = "Pending",
                    TotalAmount = totalAmount,
                    CustomerName = user.Name,
                    CustomerEmail = user.Email
                };

                return bookingDAO.CreateBooking(booking);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CreateBooking: {ex.Message}");
                return false;
            }
        }

        public List<Booking> GetUserBookings(int userId)
        {
            try
            {
                return bookingDAO.GetBookingsByUserId(userId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetUserBookings: {ex.Message}");
                return new List<Booking>();
            }
        }

        public Booking GetBookingById(int bookingId)
        {
            try
            {
                return bookingDAO.GetBookingById(bookingId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetBookingById: {ex.Message}");
                return null;
            }
        }

        public bool CancelBooking(int bookingId, int userId)
        {
            try
            {
                var booking = GetBookingById(bookingId);
                if (booking == null || booking.UserId != userId)
                {
                    return false;
                }

                // Only allow cancellation if check-in date is at least 24 hours away
                if (booking.CheckInDate.Date <= DateTime.Today.AddDays(1))
                {
                    return false;
                }

                return bookingDAO.UpdateBookingStatus(bookingId, "Cancelled");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CancelBooking: {ex.Message}");
                return false;
            }
        }

        public List<Booking> GetAllBookings()
        {
            try
            {
                return bookingDAO.GetAllBookings();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetAllBookings: {ex.Message}");
                return new List<Booking>();
            }
        }

        public List<Booking> GetRecentBookings(int count = 10)
        {
            try
            {
                return bookingDAO.GetRecentBookings(count);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetRecentBookings: {ex.Message}");
                return new List<Booking>();
            }
        }

        public List<Booking> GetUserBookingsWithDetails(int userId)
        {
            try
            {
                return bookingDAO.GetUserBookingsWithDetails(userId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetUserBookingsWithDetails: {ex.Message}");
                return new List<Booking>();
            }
        }

        public bool UpdateBookingStatus(int bookingId, string status)
        {
            try
            {
                var validStatuses = new[] { "Pending", "Confirmed", "Cancelled", "Completed", "No-Show" };
                if (Array.IndexOf(validStatuses, status) == -1)
                {
                    return false;
                }

                // Get booking info before update
                var booking = GetBookingById(bookingId);
                if (booking == null) return false;

                // Update booking status
                bool result = bookingDAO.UpdateBookingStatus(bookingId, status);

                if (result)
                {
                    // Update room availability based on status change
                    UpdateRoomAvailabilityAfterStatusChange(booking, status);
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateBookingStatus: {ex.Message}");
                return false;
            }
        }

        private void UpdateRoomAvailabilityAfterStatusChange(Booking booking, string newStatus)
        {
            try
            {
                // Khi booking chuyển từ Pending sang Confirmed
                if (booking.Status == "Pending" && newStatus == "Confirmed")
                {
                    roomManager.UpdateRoomAvailability(booking.RoomId, false);
                }
                // Khi booking chuyển sang Completed hoặc Cancelled
                else if (newStatus == "Completed" || newStatus == "Cancelled")
                {
                    // Kiểm tra xem có booking nào khác đang active cho phòng này không
                    bool hasActiveBookings = HasActiveBookingsForRoom(booking.RoomId, booking.BookingId);
                    if (!hasActiveBookings)
                    {
                        roomManager.UpdateRoomAvailability(booking.RoomId, true);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating room availability: {ex.Message}");
            }
        }

        private bool HasActiveBookingsForRoom(int roomId, int excludeBookingId)
        {
            try
            {
                var allBookings = GetAllBookings();
                return allBookings.Any(b => b.RoomId == roomId &&
                                          b.BookingId != excludeBookingId &&
                                          (b.Status == "Confirmed") &&
                                          b.CheckOutDate > DateTime.Today);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking active bookings: {ex.Message}");
                return false;
            }
        }

        //public List<Booking> GetAllBookings()
        //{
        //    try
        //    {
        //        return bookingDAO.GetAllBookings();
        //    }
        //    catch (Exception ex)
        //    {
        //        System.Diagnostics.Debug.WriteLine($"Error in GetAllBookings: {ex.Message}");
        //        return new List<Booking>();
        //    }
        //}

        public string ValidateBookingData(int roomId, int userId, DateTime checkIn, DateTime checkOut)
        {
            // Validate user
            var user = userManager.GetUserById(userId);
            if (user == null)
            {
                return "Người dùng không hợp lệ.";
            }

            // Validate room
            var room = roomManager.GetRoomById(roomId);
            if (room == null)
            {
                return "Phòng không tồn tại.";
            }

            if (!room.IsAvailable)
            {
                return "Phòng hiện không khả dụng.";
            }

            // Validate dates
            string dateValidation = roomManager.ValidateBookingDates(checkIn, checkOut);
            if (!string.IsNullOrEmpty(dateValidation))
            {
                return dateValidation;
            }

            // Check room availability for the specified dates
            if (!roomManager.IsRoomAvailable(roomId, checkIn, checkOut))
            {
                return "Phòng đã được đặt trong khoảng thời gian này.";
            }

            return string.Empty; // No errors
        }

        public Dictionary<string, object> GetBookingSummary(int roomId, DateTime checkIn, DateTime checkOut)
        {
            try
            {
                var room = roomManager.GetRoomById(roomId);
                if (room == null)
                {
                    return null;
                }

                int numberOfNights = (checkOut - checkIn).Days;
                decimal totalAmount = roomManager.CalculateRoomPrice(roomId, checkIn, checkOut);

                return new Dictionary<string, object>
                {
                    ["Room"] = room,
                    ["CheckInDate"] = checkIn,
                    ["CheckOutDate"] = checkOut,
                    ["NumberOfNights"] = numberOfNights,
                    ["PricePerNight"] = room.Price,
                    ["TotalAmount"] = totalAmount
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetBookingSummary: {ex.Message}");
                return null;
            }
        }

        public bool CanCancelBooking(int bookingId, int userId)
        {
            try
            {
                var booking = GetBookingById(bookingId);
                if (booking == null || booking.UserId != userId)
                {
                    return false;
                }

                // Can only cancel if status is Confirmed or Pending
                if (booking.Status != "Confirmed" && booking.Status != "Pending")
                {
                    return false;
                }

                // Can only cancel if check-in date is at least 24 hours away
                return booking.CheckInDate.Date > DateTime.Today.AddDays(1);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CanCancelBooking: {ex.Message}");
                return false;
            }
        }

        public Dictionary<string, int> GetBookingStatistics()
        {
            try
            {
                var bookings = GetAllBookings();
                var stats = new Dictionary<string, int>
                {
                    ["TotalBookings"] = bookings.Count,
                    ["ConfirmedBookings"] = 0,
                    ["PendingBookings"] = 0,
                    ["CancelledBookings"] = 0,
                    ["CompletedBookings"] = 0
                };

                foreach (var booking in bookings)
                {
                    switch (booking.Status)
                    {
                        case "Confirmed":
                            stats["ConfirmedBookings"]++;
                            break;
                        case "Pending":
                            stats["PendingBookings"]++;
                            break;
                        case "Cancelled":
                            stats["CancelledBookings"]++;
                            break;
                        case "Completed":
                            stats["CompletedBookings"]++;
                            break;
                    }
                }

                return stats;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetBookingStatistics: {ex.Message}");
                return new Dictionary<string, int>();
            }
        }
    }
}
