/* ===== DARK THEME HOTEL BOOKING SYSTEM ===== */

/* Root Variables */
:root {
    --primary-bg: #121212;
    --secondary-bg: #1A1A1A;
    --card-bg: #1E1E1E;
    --card-hover-bg: #2C2C2C;
    --text-primary: #FFFFFF;
    --text-secondary: #AAAAAA;
    --text-muted: #888888;
    --accent-yellow: #FFD369;
    --accent-orange: #FF914D;
    --accent-purple: #7C3AED;
    --accent-cyan: #00ADB5;
    --accent-teal: #00B894;
    --border-color: #2D2D2D;
    --border-light: #3A3A3A;
    --success: #00B894;
    --warning: #FFD369;
    --danger: #FF6B6B;
    --info: #00ADB5;
}

/* Global Styles */
body {
    background-color: var(--primary-bg) !important;
    color: var(--text-primary) !important;
    font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Navbar Styling */
.navbar-dark {
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--card-bg) 100%) !important;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--accent-yellow) !important;
    text-shadow: 0 0 10px rgba(255, 211, 105, 0.3);
}

.navbar-nav .nav-link {
    color: var(--text-primary) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-yellow) !important;
    transform: translateY(-1px);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: var(--accent-yellow);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
    left: 0;
}

/* Cards */
.card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    background-color: var(--card-hover-bg) !important;
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4) !important;
}

.card-header {
    background: linear-gradient(135deg, var(--accent-purple) 0%, var(--accent-cyan) 100%) !important;
    border-bottom: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    font-weight: 600;
}

.card-body {
    color: var(--text-primary) !important;
}

.card-footer {
    background-color: var(--secondary-bg) !important;
    border-top: 1px solid var(--border-color) !important;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--accent-orange) 0%, var(--accent-yellow) 100%) !important;
    border: none !important;
    color: var(--primary-bg) !important;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 145, 77, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 145, 77, 0.4);
    background: linear-gradient(135deg, var(--accent-yellow) 0%, var(--accent-orange) 100%) !important;
}

.btn-secondary {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-light) !important;
    color: var(--text-primary) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--card-hover-bg) !important;
    border-color: var(--accent-cyan) !important;
    color: var(--accent-cyan) !important;
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, var(--accent-teal) 100%) !important;
    border: none !important;
    color: var(--text-primary) !important;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, var(--info) 0%, var(--accent-cyan) 100%) !important;
    border: none !important;
    color: var(--text-primary) !important;
    border-radius: 8px;
}

.btn-outline-primary {
    border: 2px solid var(--accent-orange) !important;
    color: var(--accent-orange) !important;
    background: transparent !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--accent-orange) !important;
    color: var(--primary-bg) !important;
    transform: translateY(-2px);
}

/* Forms */
.form-control {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-light) !important;
    color: var(--text-primary) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-control:focus {
    background-color: var(--card-hover-bg) !important;
    border-color: var(--accent-cyan) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 173, 181, 0.25) !important;
    color: var(--text-primary) !important;
}

.form-control::placeholder {
    color: var(--text-muted) !important;
}

.form-label,
label {
    color: var(--text-primary) !important;
    font-weight: 500;
    margin-bottom: 8px;
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(0, 184, 148, 0.2) 0%, rgba(0, 184, 148, 0.1) 100%) !important;
    color: var(--success) !important;
    border-left: 4px solid var(--success);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.2) 0%, rgba(255, 107, 107, 0.1) 100%) !important;
    color: var(--danger) !important;
    border-left: 4px solid var(--danger);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 211, 105, 0.2) 0%, rgba(255, 211, 105, 0.1) 100%) !important;
    color: var(--warning) !important;
    border-left: 4px solid var(--warning);
}

.alert-info {
    background: linear-gradient(135deg, rgba(0, 173, 181, 0.2) 0%, rgba(0, 173, 181, 0.1) 100%) !important;
    color: var(--info) !important;
    border-left: 4px solid var(--info);
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 6px 12px;
}

.badge-success {
    background: var(--success) !important;
    color: var(--text-primary) !important;
}

.badge-danger {
    background: var(--danger) !important;
    color: var(--text-primary) !important;
}

/* Tables */
.table {
    color: var(--text-primary) !important;
}

.table-borderless td {
    border: none !important;
    padding: 8px 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--card-bg) 50%, var(--secondary-bg) 100%);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23FFD369" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.display-4 {
    background: linear-gradient(135deg, var(--accent-yellow) 0%, var(--accent-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    text-shadow: 0 0 30px rgba(255, 211, 105, 0.3);
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--card-bg) 0%, var(--card-hover-bg) 100%);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-purple), var(--accent-cyan), var(--accent-teal));
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

/* Breadcrumb */
.breadcrumb {
    background: transparent !important;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--accent-cyan) !important;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--accent-yellow) !important;
}

.breadcrumb-item.active {
    color: var(--text-secondary) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-cyan);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-yellow);
}

/* Dropdown */
.dropdown-menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 8px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
    color: var(--text-primary) !important;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--card-hover-bg) !important;
    color: var(--accent-yellow) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 211, 105, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, var(--accent-yellow) 0%, var(--accent-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Custom Input Focus */
.form-control:focus {
    border-color: var(--accent-cyan) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 173, 181, 0.25) !important;
    background-color: var(--card-hover-bg) !important;
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

/* Glow Effect */
.glow {
    box-shadow: 0 0 20px rgba(255, 211, 105, 0.3);
    transition: box-shadow 0.3s ease;
}

.glow:hover {
    box-shadow: 0 0 30px rgba(255, 211, 105, 0.5);
}

/* Room Card Enhancements */
.room-card {
    position: relative;
    overflow: hidden;
    border-radius: 15px !important;
}

.room-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-purple), var(--accent-cyan), var(--accent-teal));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.room-card:hover::before {
    opacity: 1;
}

/* Enhanced Button Styles */
.btn-gradient {
    background: linear-gradient(135deg, var(--accent-orange) 0%, var(--accent-yellow) 100%);
    border: none;
    color: var(--primary-bg);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-yellow) 0%, var(--accent-orange) 100%);
    transition: left 0.3s ease;
    z-index: 0;
}

.btn-gradient:hover::before {
    left: 0;
}

.btn-gradient span {
    position: relative;
    z-index: 1;
}

/* Footer Enhancements */
.footer {
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--primary-bg) 100%);
    border-top: 1px solid var(--border-color);
    padding: 40px 0 20px;
    margin-top: 60px;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }

    .display-4 {
        font-size: 2rem;
    }

    .stats-card {
        margin-bottom: 20px;
    }

    .navbar-nav .nav-link {
        padding: 10px 15px;
    }

    .card-body {
        padding: 20px;
    }

    .btn-lg {
        padding: 12px 30px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 40px 0;
    }

    .display-4 {
        font-size: 1.8rem;
    }

    .stats-number {
        font-size: 2rem;
    }

    .card {
        margin-bottom: 20px;
    }
}