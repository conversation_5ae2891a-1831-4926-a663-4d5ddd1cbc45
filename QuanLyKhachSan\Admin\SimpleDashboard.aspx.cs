using System;
using System.Web.UI;

namespace QuanLyKhachSan
{
    public partial class SimpleDashboard : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in
            if (Session["UserId"] == null)
            {
                Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }
            
            // Check if user is admin
            if (Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadBasicData();
            }
        }

        private void LoadBasicData()
        {
            try
            {
                // Display admin name
                lblAdminName.Text = Session["UserName"]?.ToString() ?? "Admin";

                // Set some basic numbers (you can replace with real data later)
                lblTotalRooms.Text = "10";
                lblTotalBookings.Text = "25";
                lblTotalUsers.Text = "15";
                lblTotalRevenue.Text = "50,000,000";

                ShowMessage("Dashboard loaded successfully!", "alert-success");
            }
            catch (Exception ex)
            {
                ShowMessage("Error loading dashboard: " + ex.Message, "alert-danger");
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard: {ex.Message}");
            }
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }
    }
}
