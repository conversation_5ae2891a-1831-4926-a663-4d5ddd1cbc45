using System;
using System.Linq;
using System.Web.UI;
using QuanLyKhachSan.BLL;

namespace QuanLyKhachSan
{
    public partial class SimpleDashboard : Page
    {
        private RoomManager roomManager;
        private BookingManager bookingManager;
        private UserManager userManager;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in
            if (Session["UserId"] == null)
            {
                Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }

            // Check if user is admin
            if (Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Default.aspx");
                return;
            }

            // Initialize managers
            roomManager = new RoomManager();
            bookingManager = new BookingManager();
            userManager = new UserManager();

            if (!IsPostBack)
            {
                LoadDashboardData();
            }
        }

        private void LoadDashboardData()
        {
            try
            {
                // Display admin name
                lblAdminName.Text = Session["UserName"]?.ToString() ?? "Admin";
                lblAdminInfo.Text = Session["UserName"]?.ToString() ?? "Admin";

                // Load real data from database
                LoadRoomStatistics();
                LoadBookingStatistics();
                LoadUserStatistics();
                LoadRevenueStatistics();

                ShowMessage("Dashboard loaded successfully!", "alert-success");
            }
            catch (Exception ex)
            {
                ShowMessage("Error loading dashboard: " + ex.Message, "alert-danger");
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard: {ex.Message}");
            }
        }

        private void LoadRoomStatistics()
        {
            try
            {
                var allRooms = roomManager.GetAllRooms();
                lblTotalRooms.Text = allRooms.Count.ToString();

                var availableRooms = allRooms.Where(r => r.IsAvailable).Count();
                var occupiedRooms = allRooms.Count - availableRooms;

                lblAvailableRooms.Text = availableRooms.ToString();
                lblOccupiedRooms.Text = occupiedRooms.ToString();
            }
            catch (Exception ex)
            {
                lblTotalRooms.Text = "Error";
                System.Diagnostics.Debug.WriteLine($"Error loading room statistics: {ex.Message}");
            }
        }

        private void LoadBookingStatistics()
        {
            try
            {
                var allBookings = bookingManager.GetAllBookings();
                lblTotalBookings.Text = allBookings.Count.ToString();

                // Today's bookings
                var today = DateTime.Today;
                var todayBookings = allBookings.Where(b => b.BookingDate.Date == today).Count();
                lblTodayBookings.Text = todayBookings.ToString();
            }
            catch (Exception ex)
            {
                lblTotalBookings.Text = "Error";
                lblTodayBookings.Text = "Error";
                System.Diagnostics.Debug.WriteLine($"Error loading booking statistics: {ex.Message}");
            }
        }

        private void LoadUserStatistics()
        {
            try
            {
                var allUsers = userManager.GetAllUsers();
                var customerCount = allUsers.Where(u => u.Role == "Customer").Count();
                lblTotalUsers.Text = customerCount.ToString();
            }
            catch (Exception ex)
            {
                lblTotalUsers.Text = "Error";
                System.Diagnostics.Debug.WriteLine($"Error loading user statistics: {ex.Message}");
            }
        }

        private void LoadRevenueStatistics()
        {
            try
            {
                var allBookings = bookingManager.GetAllBookings();

                // Total revenue from confirmed and completed bookings
                var totalRevenue = allBookings
                    .Where(b => b.Status == "Confirmed" || b.Status == "Completed")
                    .Sum(b => b.TotalAmount);
                lblTotalRevenue.Text = totalRevenue.ToString("N0");

                // Today's revenue
                var today = DateTime.Today;
                var todayRevenue = allBookings
                    .Where(b => b.BookingDate.Date == today && (b.Status == "Confirmed" || b.Status == "Completed"))
                    .Sum(b => b.TotalAmount);
                lblTodayRevenue.Text = todayRevenue.ToString("N0");
            }
            catch (Exception ex)
            {
                lblTotalRevenue.Text = "Error";
                lblTodayRevenue.Text = "Error";
                System.Diagnostics.Debug.WriteLine($"Error loading revenue statistics: {ex.Message}");
            }
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }
    }
}
