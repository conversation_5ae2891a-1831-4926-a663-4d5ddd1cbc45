﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.master.cs" Inherits="QuanLyKhachSan.SiteMaster" %>

<!DOCTYPE html>

<html lang="en">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%: Page.Title %> - Hệ thống đặt phòng khách sạn</title>

    <asp:PlaceHolder runat="server">
        <%: Scripts.Render("~/bundles/modernizr") %>
    </asp:PlaceHolder>

    <webopt:bundlereference runat="server" path="~/Content/css" />
    <link href="~/Content/custom-light-theme.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <link href="~/favicon.ico" rel="shortcut icon" type="image/x-icon" />

</head>
<body>
    <form runat="server">
        <asp:ScriptManager runat="server">
            <Scripts>
                <%--To learn more about bundling scripts in ScriptManager see https://go.microsoft.com/fwlink/?LinkID=301884 --%>
                <%--Framework Scripts--%>
                <asp:ScriptReference Name="MsAjaxBundle" />
                <asp:ScriptReference Name="jquery" />
                <asp:ScriptReference Name="WebForms.js" Assembly="System.Web" Path="~/Scripts/WebForms/WebForms.js" />
                <asp:ScriptReference Name="WebUIValidation.js" Assembly="System.Web" Path="~/Scripts/WebForms/WebUIValidation.js" />
                <asp:ScriptReference Name="MenuStandards.js" Assembly="System.Web" Path="~/Scripts/WebForms/MenuStandards.js" />
                <asp:ScriptReference Name="GridView.js" Assembly="System.Web" Path="~/Scripts/WebForms/GridView.js" />
                <asp:ScriptReference Name="DetailsView.js" Assembly="System.Web" Path="~/Scripts/WebForms/DetailsView.js" />
                <asp:ScriptReference Name="TreeView.js" Assembly="System.Web" Path="~/Scripts/WebForms/TreeView.js" />
                <asp:ScriptReference Name="WebParts.js" Assembly="System.Web" Path="~/Scripts/WebForms/WebParts.js" />
                <asp:ScriptReference Name="Focus.js" Assembly="System.Web" Path="~/Scripts/WebForms/Focus.js" />
                <asp:ScriptReference Name="WebFormsBundle" />
                <%--Site Scripts--%>
            </Scripts>
        </asp:ScriptManager>

        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark">
            <div class="container">
                <a class="navbar-brand" runat="server" href="~/">
                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                </a>
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" title="Toggle navigation" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" runat="server" href="~/">
                                <i class="fas fa-home me-1"></i>Trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" runat="server" href="~/RoomList.aspx">
                                <i class="fas fa-bed me-1"></i>Danh sách phòng
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" runat="server" href="~/About">
                                <i class="fas fa-info-circle me-1"></i>Giới thiệu
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" runat="server" href="~/Contact">
                                <i class="fas fa-envelope me-1"></i>Liên hệ
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <asp:Panel ID="pnlUserNotLoggedIn" runat="server">
                            <li class="nav-item">
                                <a class="nav-link" runat="server" href="~/Login.aspx">
                                    <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" runat="server" href="~/Register.aspx">
                                    <i class="fas fa-user-plus me-1"></i>Đăng ký
                                </a>
                            </li>
                        </asp:Panel>
                        <asp:Panel ID="pnlUserLoggedIn" runat="server" Visible="false">
                            <asp:Panel ID="pnlAdminMenu" runat="server" Visible="false">
                                <li class="nav-item">
                                    <a class="nav-link" runat="server" href="~/Admin/WorkingDashboard.aspx">
                                        <i class="fas fa-tachometer-alt me-1"></i>Admin
                                    </a>
                                </li>
                            </asp:Panel>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle me-1"></i>
                                    Xin chào, <asp:Label ID="lblUserName" runat="server"></asp:Label>
                                </a>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="MyProfile.aspx">
                                            <i class="fas fa-user me-2"></i>Hồ sơ của tôi
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="MyBookings.aspx">
                                            <i class="fas fa-calendar-check me-2"></i>Đặt phòng của tôi
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <asp:LinkButton ID="lnkLogout" runat="server" CssClass="dropdown-item" OnClick="lnkLogout_Click">
                                            <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                                        </asp:LinkButton>
                                    </li>
                                </ul>
                            </li>
                        </asp:Panel>
                    </ul>
                </div>
            </div>
        </nav>
        <div class="container body-content">
            <asp:ContentPlaceHolder ID="MainContent" runat="server">
            </asp:ContentPlaceHolder>
            <hr />
            <footer>
                <p>&copy; <%: DateTime.Now.Year %> - My ASP.NET Application</p>
            </footer>
        </div>
    </form>
    <asp:PlaceHolder runat="server">
        <%: Scripts.Render("~/Scripts/bootstrap.js") %>
    </asp:PlaceHolder>
</body>
</html>
