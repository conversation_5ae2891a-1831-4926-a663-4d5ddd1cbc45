<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html>
<head>
    <title>Quick Test</title>
    <link href="Content/bootstrap.min.css" rel="stylesheet" />
    <link href="Content/custom-light-theme.css" rel="stylesheet" />
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header">
                <h3>Quick ASP.NET Test</h3>
            </div>
            <div class="card-body">
                <h4>Server Time: <%= DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") %></h4>
                
                <div class="alert alert-success">
                    <strong>Thành công!</strong> ASP.NET đang hoạt động.
                </div>
                
                <h4>Session Information:</h4>
                <ul class="list-group">
                    <li class="list-group-item">
                        <strong>Session ID:</strong> <%= Session.SessionID %>
                    </li>
                    <li class="list-group-item">
                        <strong>User ID:</strong> <%= Session["UserId"] ?? "Not logged in" %>
                    </li>
                    <li class="list-group-item">
                        <strong>User Name:</strong> <%= Session["UserName"] ?? "N/A" %>
                    </li>
                    <li class="list-group-item">
                        <strong>User Role:</strong> <%= Session["UserRole"] ?? "N/A" %>
                    </li>
                </ul>
                
                <hr>
                
                <h4>Navigation:</h4>
                <div class="d-flex gap-2 flex-wrap">
                    <a href="Default.aspx" class="btn btn-primary">Default.aspx</a>
                    <a href="Test.aspx" class="btn btn-success">Test.aspx</a>
                    <a href="Login.aspx" class="btn btn-info">Login.aspx</a>
                    <a href="Admin/AdminTest.aspx" class="btn btn-warning">Admin Test</a>
                    <a href="SimpleTest.html" class="btn btn-secondary">HTML Test</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
