<%@ Page Title="Đặt phòng của tôi" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MyBookings.aspx.cs" Inherits="QuanLyKhachSan.MyBookings" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1 class="mb-3" style="color: var(--text-primary); font-weight: 600;">
                    <i class="fas fa-calendar-check me-3" style="color: var(--accent-blue);"></i>
                    Đặt phòng của tôi
                </h1>
                <p class="lead" style="color: var(--text-secondary);">
                    Quản lý và theo dõi tất cả các đặt phòng của bạn
                </p>
            </div>
        </div>

        <!-- Alert Messages -->
        <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
            <asp:Label ID="lblMessage" runat="server"></asp:Label>
        </asp:Panel>

        <!-- Filter and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2" style="color: var(--accent-mint);"></i>
                            Bộ lọc
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Trạng thái:</label>
                                <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select" 
                                    AutoPostBack="true" OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                                    <asp:ListItem Value="" Text="Tất cả trạng thái"></asp:ListItem>
                                    <asp:ListItem Value="Confirmed" Text="Đã xác nhận"></asp:ListItem>
                                    <asp:ListItem Value="Pending" Text="Chờ xác nhận"></asp:ListItem>
                                    <asp:ListItem Value="Completed" Text="Hoàn thành"></asp:ListItem>
                                    <asp:ListItem Value="Cancelled" Text="Đã hủy"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Từ ngày:</label>
                                <asp:TextBox ID="txtFromDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Đến ngày:</label>
                                <asp:TextBox ID="txtToDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <asp:Button ID="btnFilter" runat="server" Text="Áp dụng bộ lọc" 
                                    CssClass="btn btn-primary me-2" OnClick="btnFilter_Click" />
                                <asp:Button ID="btnClearFilter" runat="server" Text="Xóa bộ lọc" 
                                    CssClass="btn btn-secondary" OnClick="btnClearFilter_Click" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bookings List -->
        <div class="row">
            <div class="col-12">
                <asp:Repeater ID="rptBookings" runat="server" OnItemCommand="rptBookings_ItemCommand">
                    <ItemTemplate>
                        <div class="card mb-4 fade-in">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1" style="color: var(--text-primary);">
                                        <i class="fas fa-bed me-2" style="color: var(--accent-blue);"></i>
                                        Phòng <%# Eval("RoomNumber") %> - <%# Eval("RoomType") %>
                                    </h5>
                                    <small style="color: var(--text-muted);">
                                        Mã đặt phòng: #<%# Eval("BookingId") %>
                                    </small>
                                </div>
                                <span class="badge <%# GetStatusBadgeClass(Eval("Status").ToString()) %> fs-6">
                                    <%# GetStatusText(Eval("Status").ToString()) %>
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="row mb-3">
                                            <div class="col-sm-6">
                                                <p class="mb-2">
                                                    <i class="fas fa-calendar-alt me-2" style="color: var(--accent-mint);"></i>
                                                    <strong>Nhận phòng:</strong><br>
                                                    <span style="color: var(--text-secondary);"><%# Eval("CheckInDate", "{0:dd/MM/yyyy}") %></span>
                                                </p>
                                            </div>
                                            <div class="col-sm-6">
                                                <p class="mb-2">
                                                    <i class="fas fa-calendar-check me-2" style="color: var(--accent-orange);"></i>
                                                    <strong>Trả phòng:</strong><br>
                                                    <span style="color: var(--text-secondary);"><%# Eval("CheckOutDate", "{0:dd/MM/yyyy}") %></span>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-sm-6">
                                                <p class="mb-2">
                                                    <i class="fas fa-clock me-2" style="color: var(--accent-yellow);"></i>
                                                    <strong>Ngày đặt:</strong><br>
                                                    <span style="color: var(--text-secondary);"><%# Eval("BookingDate", "{0:dd/MM/yyyy HH:mm}") %></span>
                                                </p>
                                            </div>
                                            <div class="col-sm-6">
                                                <p class="mb-2">
                                                    <i class="fas fa-moon me-2" style="color: var(--accent-purple);"></i>
                                                    <strong>Số đêm:</strong><br>
                                                    <span style="color: var(--text-secondary);"><%# GetNumberOfNights((DateTime)Eval("CheckInDate"), (DateTime)Eval("CheckOutDate")) %> đêm</span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="mb-3">
                                            <h4 style="color: var(--accent-orange);">
                                                <%# Eval("TotalAmount", "{0:N0}") %> VNĐ
                                            </h4>
                                        </div>
                                        <div class="d-grid gap-2">
                                            <asp:Button ID="btnViewDetails" runat="server" Text="Xem chi tiết" 
                                                CssClass="btn btn-outline-primary" 
                                                CommandName="ViewDetails" CommandArgument='<%# Eval("BookingId") %>' />
                                            <asp:Button ID="btnCancelBooking" runat="server" Text="Hủy đặt phòng" 
                                                CssClass="btn btn-outline-danger" 
                                                CommandName="CancelBooking" CommandArgument='<%# Eval("BookingId") %>'
                                                Visible='<%# CanCancelBooking(Eval("Status").ToString(), (DateTime)Eval("CheckInDate")) %>'
                                                OnClientClick="return confirm('Bạn có chắc chắn muốn hủy đặt phòng này?');" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                </asp:Repeater>

                <!-- No Bookings Message -->
                <asp:Panel ID="pnlNoBookings" runat="server" Visible="false">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-calendar-times fa-4x mb-4" style="color: var(--text-muted);"></i>
                            <h4 style="color: var(--text-secondary);">Không tìm thấy đặt phòng nào</h4>
                            <p style="color: var(--text-muted);">
                                Bạn chưa có đặt phòng nào hoặc không có đặt phòng nào phù hợp với bộ lọc.
                            </p>
                            <a href="RoomList.aspx" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Tìm phòng ngay
                            </a>
                        </div>
                    </div>
                </asp:Panel>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2" style="color: var(--accent-blue);"></i>
                            Thống kê tổng quan
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h3 style="color: var(--accent-blue);">
                                    <asp:Label ID="lblTotalBookings" runat="server" Text="0"></asp:Label>
                                </h3>
                                <p style="color: var(--text-secondary);">Tổng đặt phòng</p>
                            </div>
                            <div class="col-md-3">
                                <h3 style="color: var(--accent-mint);">
                                    <asp:Label ID="lblConfirmedBookings" runat="server" Text="0"></asp:Label>
                                </h3>
                                <p style="color: var(--text-secondary);">Đã xác nhận</p>
                            </div>
                            <div class="col-md-3">
                                <h3 style="color: var(--accent-orange);">
                                    <asp:Label ID="lblTotalSpent" runat="server" Text="0"></asp:Label>
                                </h3>
                                <p style="color: var(--text-secondary);">Tổng chi tiêu (VNĐ)</p>
                            </div>
                            <div class="col-md-3">
                                <h3 style="color: var(--accent-yellow);">
                                    <asp:Label ID="lblTotalNights" runat="server" Text="0"></asp:Label>
                                </h3>
                                <p style="color: var(--text-secondary);">Tổng số đêm</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
