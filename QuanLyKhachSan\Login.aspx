<%@ Page Title="Đăng nhập" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true"
    CodeBehind="Login.aspx.cs" Inherits="QuanLyKhachSan.Login" %>

    <asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="card fade-in">
                        <div class="card-header text-center">
                            <div class="mb-3">
                                <i class="fas fa-sign-in-alt fa-3x" style="color: var(--accent-blue);"></i>
                            </div>
                            <h3 style="color: var(--text-primary); font-weight: 600;">Đăng nhập</h3>
                            <p style="color: var(--text-secondary);">Chào mừng bạn quay trở lại</p>
                        </div>
                        <div class="card-body">
                            <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
                                <asp:Label ID="lblMessage" runat="server"></asp:Label>
                            </asp:Panel>

                            <div class="form-group mb-4">
                                <label for="txtEmail" class="form-label">
                                    <i class="fas fa-envelope me-2" style="color: var(--accent-blue);"></i>
                                    Email:
                                </label>
                                <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" TextMode="Email"
                                    placeholder="Nhập địa chỉ email của bạn"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail"
                                    ErrorMessage="Vui lòng nhập email" CssClass="text-danger" Display="Dynamic">
                                </asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revEmail" runat="server"
                                    ControlToValidate="txtEmail" ErrorMessage="Email không hợp lệ"
                                    ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                                    CssClass="text-danger" Display="Dynamic">
                                </asp:RegularExpressionValidator>
                            </div>

                            <div class="form-group mb-4">
                                <label for="txtPassword" class="form-label">
                                    <i class="fas fa-lock me-2" style="color: var(--accent-mint);"></i>
                                    Mật khẩu:
                                </label>
                                <asp:TextBox ID="txtPassword" runat="server" CssClass="form-control" TextMode="Password"
                                    placeholder="Nhập mật khẩu của bạn"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvPassword" runat="server"
                                    ControlToValidate="txtPassword" ErrorMessage="Vui lòng nhập mật khẩu"
                                    CssClass="text-danger" Display="Dynamic">
                                </asp:RequiredFieldValidator>
                            </div>

                            <div class="d-grid mb-4">
                                <asp:Button ID="btnLogin" runat="server" Text="Đăng nhập"
                                    CssClass="btn btn-primary btn-lg py-3" OnClick="btnLogin_Click" />
                            </div>

                            <div class="text-center">
                                <p style="color: var(--text-secondary);">
                                    Chưa có tài khoản?
                                    <a href="Register.aspx"
                                        style="color: var(--accent-blue); text-decoration: none; font-weight: 500;">
                                        Đăng ký ngay
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </asp:Content>