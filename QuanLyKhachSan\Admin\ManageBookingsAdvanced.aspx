<%@ Page Language="C#" AutoEventWireup="true" %>
    <%@ Import Namespace="QuanLyKhachSan.BLL" %>
        <%@ Import Namespace="QuanLyKhachSan.Models" %>
            <%@ Import Namespace="System.Linq" %>

                <!DOCTYPE html>
                <html>

                <head>
                    <title>Quản lý đặt phòng - Nâng cao</title>
                    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
                    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
                        rel="stylesheet" />
                </head>

                <body>
                    <script runat="server">
        private BookingManager bookingManager;
        private List < Booking > bookings;
        private string message = "";
        private string messageClass = "";

        protected void Page_Load(object sender, EventArgs e)
                        {
                            // Check if user is logged in and is admin
                            if (Session["UserId"] == null) {
                                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                                return;
                            }

                            if (Session["UserRole"]?.ToString() != "Admin") {
                                Response.Redirect("../Default.aspx");
                                return;
                            }

                            bookingManager = new BookingManager();

                            // Handle form submissions
                            if (Request.HttpMethod == "POST") {
                                HandlePostRequest();
                            }

                            LoadBookings();
                        }

        private void HandlePostRequest()
                        {
            string action = Request.Form["action"];

                            try {
                                switch (action) {
                                    case "confirm_booking":
                        int confirmBookingId = int.Parse(Request.Form["bookingId"]);
                        bool confirmResult = bookingManager.UpdateBookingStatus(confirmBookingId, "Confirmed");
                                        if (confirmResult) {
                                            message = "Đặt phòng đã được xác nhận thành công!";
                                            messageClass = "alert-success";
                                        }
                                        else {
                                            message = "Có lỗi khi xác nhận đặt phòng!";
                                            messageClass = "alert-danger";
                                        }
                                        break;

                                    case "cancel_booking":
                        int cancelBookingId = int.Parse(Request.Form["bookingId"]);
                        bool cancelResult = bookingManager.UpdateBookingStatus(cancelBookingId, "Cancelled");
                                        if (cancelResult) {
                                            message = "Đặt phòng đã được hủy thành công!";
                                            messageClass = "alert-warning";
                                        }
                                        else {
                                            message = "Có lỗi khi hủy đặt phòng!";
                                            messageClass = "alert-danger";
                                        }
                                        break;

                                    case "complete_booking":
                        int completeBookingId = int.Parse(Request.Form["bookingId"]);
                        bool completeResult = bookingManager.UpdateBookingStatus(completeBookingId, "Completed");
                                        if (completeResult) {
                                            message = "Đặt phòng đã được đánh dấu hoàn thành!";
                                            messageClass = "alert-info";
                                        }
                                        else {
                                            message = "Có lỗi khi hoàn thành đặt phòng!";
                                            messageClass = "alert-danger";
                                        }
                                        break;
                                }
                            }
                            catch (Exception ex)
                            {
                                message = "Có lỗi xảy ra: " + ex.Message;
                                messageClass = "alert-danger";
                            }
                        }

        private void LoadBookings()
                        {
                            try {
                                bookings = bookingManager.GetAllBookings();
                            }
                            catch (Exception ex)
                            {
                                message = "Có lỗi khi tải dữ liệu đặt phòng: " + ex.Message;
                                messageClass = "alert-danger";
                                bookings = new List < Booking > ();
                            }
                        }

        // Helper methods for UI
        protected string GetStatusText(string status)
                        {
                            switch (status?.ToLower()) {
                                case "confirmed": return "Đã xác nhận";
                                case "pending": return "Chờ xác nhận";
                                case "cancelled": return "Đã hủy";
                                case "completed": return "Hoàn thành";
                                default: return status ?? "N/A";
                            }
                        }

        protected string GetStatusBadgeClass(string status)
                        {
                            switch (status?.ToLower()) {
                                case "confirmed": return "bg-success";
                                case "pending": return "bg-warning text-dark";
                                case "cancelled": return "bg-danger";
                                case "completed": return "bg-info";
                                default: return "bg-secondary";
                            }
                        }

        protected string GetStatusColor(string status)
                        {
                            switch (status?.ToLower()) {
                                case "confirmed": return "#28a745";
                                case "pending": return "#ffc107";
                                case "cancelled": return "#dc3545";
                                case "completed": return "#17a2b8";
                                default: return "#6c757d";
                            }
                        }

        protected bool CanConfirm(string status)
                        {
                            return status?.ToLower() == "pending";
                        }

        protected bool CanCancel(string status)
                        {
                            return status?.ToLower() == "pending" || status?.ToLower() == "confirmed";
                        }

        protected bool CanComplete(string status, DateTime checkOutDate)
                        {
                            return status?.ToLower() == "confirmed" && DateTime.Now.Date >= checkOutDate.Date;
                        }

        protected int GetNumberOfNights(DateTime checkIn, DateTime checkOut)
                        {
                            return Math.Max(1, (checkOut - checkIn).Days);
                        }
                    </script>

                    <div class="container-fluid mt-4">
                        <!-- Navigation -->
                        <nav class="navbar navbar-expand-lg navbar-dark mb-4">
                            <div class="container-fluid">
                                <a class="navbar-brand" href="../Default.aspx">
                                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                                </a>
                                <div class="navbar-nav ms-auto">
                                    <a class="nav-link" href="WorkingDashboard.aspx">
                                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                    </a>
                                    <a class="nav-link" href="ManageRoomsAdvanced.aspx">
                                        <i class="fas fa-bed me-1"></i>Phòng
                                    </a>
                                    <a class="nav-link active" href="ManageBookingsAdvanced.aspx">
                                        <i class="fas fa-calendar-check me-1"></i>Đặt phòng
                                    </a>
                                    <a class="nav-link" href="ManageUsers.aspx">
                                        <i class="fas fa-users me-1"></i>Người dùng
                                    </a>
                                </div>
                            </div>
                        </nav>

                        <!-- Page Header -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                                    <i class="fas fa-calendar-check me-3" style="color: var(--accent-blue);"></i>
                                    Quản lý đặt phòng nâng cao
                                </h1>
                                <p class="lead" style="color: var(--text-secondary);">
                                    Xem, xác nhận, hủy và quản lý tất cả đặt phòng trong hệ thống
                                </p>
                            </div>
                        </div>

                        <!-- Messages -->
                        <% if (!string.IsNullOrEmpty(message)) { %>
                            <div class="alert <%= messageClass %> alert-dismissible fade show" role="alert">
                                <%= message %>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <% } %>

                                <!-- Statistics Cards -->
                                <div class="row mb-4">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="stats-card">
                                            <div class="stats-number" style="color: var(--accent-blue);">
                                                <i class="fas fa-calendar-check me-2"></i>
                                                <%= bookings?.Count ?? 0 %>
                                            </div>
                                            <p style="color: var(--text-secondary); font-weight: 500;">Tổng đặt phòng
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="stats-card">
                                            <div class="stats-number" style="color: var(--accent-mint);">
                                                <i class="fas fa-check-circle me-2"></i>
                                                <%= bookings?.Where(b=> b.Status == "Confirmed").Count() ?? 0 %>
                                            </div>
                                            <p style="color: var(--text-secondary); font-weight: 500;">Đã xác nhận</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="stats-card">
                                            <div class="stats-number" style="color: var(--accent-yellow);">
                                                <i class="fas fa-clock me-2"></i>
                                                <%= bookings?.Where(b=> b.Status == "Pending").Count() ?? 0 %>
                                            </div>
                                            <p style="color: var(--text-secondary); font-weight: 500;">Chờ xác nhận</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="stats-card">
                                            <div class="stats-number" style="color: var(--accent-orange);">
                                                <i class="fas fa-dollar-sign me-2"></i>
                                                <%= bookings?.Where(b=> b.Status == "Confirmed" || b.Status ==
                                                    "Completed").Sum(b => b.TotalAmount).ToString("N0") ?? "0" %>
                                            </div>
                                            <p style="color: var(--text-secondary); font-weight: 500;">Doanh thu (VNĐ)
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Bookings List -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-list me-2" style="color: var(--accent-blue);"></i>
                                                    Danh sách đặt phòng
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <% if (bookings !=null && bookings.Any()) { %>
                                                    <% foreach (var booking in bookings.Take(20)) { %>
                                                        <div class="card mb-3 border-start border-4"
                                                            style="border-left-color: <%= GetStatusColor(booking.Status) %> !important;">
                                                            <div class="card-body">
                                                                <div class="row align-items-center">
                                                                    <div class="col-md-7">
                                                                        <div
                                                                            class="d-flex justify-content-between align-items-start mb-2">
                                                                            <h6 class="mb-1"
                                                                                style="color: var(--text-primary);">
                                                                                <i class="fas fa-bed me-2"
                                                                                    style="color: var(--accent-blue);"></i>
                                                                                Phòng <%= booking.Room?.RoomNumber
                                                                                    ?? "N/A" %> - <%=
                                                                                        booking.Room?.RoomType ?? "N/A"
                                                                                        %>
                                                                                        <small class="text-muted">(ID: #
                                                                                            <%= booking.BookingId %>
                                                                                                )
                                                                                        </small>
                                                                            </h6>
                                                                            <span
                                                                                class="badge <%= GetStatusBadgeClass(booking.Status) %>">
                                                                                <%= GetStatusText(booking.Status) %>
                                                                            </span>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-sm-6">
                                                                                <p class="mb-1"
                                                                                    style="color: var(--text-secondary); font-size: 0.9rem;">
                                                                                    <i class="fas fa-user me-2"
                                                                                        style="color: var(--accent-mint);"></i>
                                                                                    Khách hàng: <strong>
                                                                                        <%= booking.CustomerName %>
                                                                                    </strong>
                                                                                </p>
                                                                                <p class="mb-1"
                                                                                    style="color: var(--text-secondary); font-size: 0.9rem;">
                                                                                    <i class="fas fa-envelope me-2"
                                                                                        style="color: var(--accent-yellow);"></i>
                                                                                    Email: <%= booking.CustomerEmail %>
                                                                                </p>
                                                                            </div>
                                                                            <div class="col-sm-6">
                                                                                <p class="mb-1"
                                                                                    style="color: var(--text-secondary); font-size: 0.9rem;">
                                                                                    <i class="fas fa-calendar-alt me-2"
                                                                                        style="color: var(--accent-mint);"></i>
                                                                                    Nhận: <%=
                                                                                        booking.CheckInDate.ToString("dd/MM/yyyy")
                                                                                        %>
                                                                                </p>
                                                                                <p class="mb-1"
                                                                                    style="color: var(--text-secondary); font-size: 0.9rem;">
                                                                                    <i class="fas fa-calendar-check me-2"
                                                                                        style="color: var(--accent-orange);"></i>
                                                                                    Trả: <%=
                                                                                        booking.CheckOutDate.ToString("dd/MM/yyyy")
                                                                                        %>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                        <p class="mb-1"
                                                                            style="color: var(--text-secondary); font-size: 0.9rem;">
                                                                            <i class="fas fa-clock me-2"
                                                                                style="color: var(--accent-purple);"></i>
                                                                            Đặt ngày: <%=
                                                                                booking.BookingDate.ToString("dd/MM/yyyy")
                                                                                %>
                                                                                <%= booking.BookingDate.ToString("HH:mm")
                                                                                    %> | <%=
                                                                                        GetNumberOfNights(booking.CheckInDate,
                                                                                        booking.CheckOutDate) %> đêm
                                                                        </p>
                                                                    </div>
                                                                    <div class="col-md-2 text-center">
                                                                        <h5 class="mb-2"
                                                                            style="color: var(--accent-orange);">
                                                                            <%= booking.TotalAmount.ToString("N0") %>
                                                                                VNĐ
                                                                        </h5>
                                                                        <small style="color: var(--text-muted);">
                                                                            <%= (booking.TotalAmount /
                                                                                GetNumberOfNights(booking.CheckInDate,
                                                                                booking.CheckOutDate)).ToString("N0") %>
                                                                                VNĐ/đêm
                                                                        </small>
                                                                    </div>
                                                                    <div class="col-md-3">
                                                                        <div class="d-grid gap-2">
                                                                            <button
                                                                                class="btn btn-outline-primary btn-sm"
                                                                                data-booking-id="<%= booking.BookingId %>"
                                                                                data-room-number="<%= booking.Room?.RoomNumber %>"
                                                                                data-customer-name="<%= booking.CustomerName %>"
                                                                                data-check-in="<%= booking.CheckInDate.Day %>/<%= booking.CheckInDate.Month %>/<%= booking.CheckInDate.Year %>"
                                                                                data-check-out="<%= booking.CheckOutDate.Day %>/<%= booking.CheckOutDate.Month %>/<%= booking.CheckOutDate.Year %>"
                                                                                data-total-amount="<%= booking.TotalAmount %>"
                                                                                data-status="<%= booking.Status %>"
                                                                                onclick="showBookingDetailsFromData(this)">
                                                                                <i class="fas fa-eye me-2"></i>Chi
                                                                                tiết
                                                                            </button>

                                                                            <% if (CanConfirm(booking.Status)) { %>
                                                                                <form method="post"
                                                                                    style="display: inline;">
                                                                                    <input type="hidden" name="action"
                                                                                        value="confirm_booking" />
                                                                                    <input type="hidden"
                                                                                        name="bookingId"
                                                                                        value="<%= booking.BookingId %>" />
                                                                                    <button type="submit"
                                                                                        class="btn btn-outline-success btn-sm w-100"
                                                                                        onclick="return confirm('Bạn có chắc muốn xác nhận đặt phòng này?')">
                                                                                        <i
                                                                                            class="fas fa-check me-2"></i>Xác
                                                                                        nhận
                                                                                    </button>
                                                                                </form>
                                                                                <% } %>

                                                                                    <% if (CanComplete(booking.Status,
                                                                                        booking.CheckOutDate)) { %>
                                                                                        <form method="post"
                                                                                            style="display: inline;">
                                                                                            <input type="hidden"
                                                                                                name="action"
                                                                                                value="complete_booking" />
                                                                                            <input type="hidden"
                                                                                                name="bookingId"
                                                                                                value="<%= booking.BookingId %>" />
                                                                                            <button type="submit"
                                                                                                class="btn btn-outline-info btn-sm w-100"
                                                                                                onclick="return confirm('Đánh dấu đặt phòng này đã hoàn thành?')">
                                                                                                <i
                                                                                                    class="fas fa-flag-checkered me-2"></i>Hoàn
                                                                                                thành
                                                                                            </button>
                                                                                        </form>
                                                                                        <% } %>

                                                                                            <% if
                                                                                                (CanCancel(booking.Status))
                                                                                                { %>
                                                                                                <form method="post"
                                                                                                    style="display: inline;">
                                                                                                    <input type="hidden"
                                                                                                        name="action"
                                                                                                        value="cancel_booking" />
                                                                                                    <input type="hidden"
                                                                                                        name="bookingId"
                                                                                                        value="<%= booking.BookingId %>" />
                                                                                                    <button
                                                                                                        type="submit"
                                                                                                        class="btn btn-outline-danger btn-sm w-100"
                                                                                                        onclick="return confirm('Bạn có chắc muốn hủy đặt phòng này?')">
                                                                                                        <i
                                                                                                            class="fas fa-times me-2"></i>Hủy
                                                                                                    </button>
                                                                                                </form>
                                                                                                <% } %>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <% } %>

                                                            <% if (bookings.Count> 20) { %>
                                                                <div class="alert alert-info text-center">
                                                                    <i class="fas fa-info-circle me-2"></i>
                                                                    Hiển thị 20 đặt phòng gần nhất. Tổng cộng có <%=
                                                                        bookings.Count %> đặt phòng.
                                                                </div>
                                                                <% } %>
                                                                    <% } else { %>
                                                                        <div class="text-center py-5">
                                                                            <i class="fas fa-calendar-times fa-4x mb-4"
                                                                                style="color: var(--text-muted);"></i>
                                                                            <h4 style="color: var(--text-secondary);">
                                                                                Không có đặt phòng nào</h4>
                                                                            <p style="color: var(--text-muted);">Hệ
                                                                                thống chưa có dữ liệu đặt phòng hoặc có
                                                                                lỗi kết nối database.</p>
                                                                        </div>
                                                                        <% } %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                    </div>

                    <!-- Booking Details Modal -->
                    <div class="modal fade" id="bookingDetailsModal" tabindex="-1"
                        aria-labelledby="bookingDetailsModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="bookingDetailsModalLabel">
                                        <i class="fas fa-info-circle me-2" style="color: var(--accent-blue);"></i>
                                        Chi tiết đặt phòng
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 style="color: var(--text-primary);">Thông tin đặt phòng</h6>
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <span>Mã đặt phòng:</span>
                                                    <strong id="detailBookingId"></strong>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <span>Phòng:</span>
                                                    <strong id="detailRoomNumber"></strong>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <span>Khách hàng:</span>
                                                    <strong id="detailCustomerName"></strong>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <span>Trạng thái:</span>
                                                    <span id="detailStatus" class="badge"></span>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 style="color: var(--text-primary);">Thông tin thời gian</h6>
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <span>Ngày nhận phòng:</span>
                                                    <strong id="detailCheckIn"></strong>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <span>Ngày trả phòng:</span>
                                                    <strong id="detailCheckOut"></strong>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between">
                                                    <span>Tổng tiền:</span>
                                                    <strong id="detailTotalAmount"
                                                        style="color: var(--accent-orange);"></strong>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        <i class="fas fa-times me-2"></i>Đóng
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script src="../Scripts/bootstrap.bundle.min.js"></script>
                    <script>
                        function showBookingDetailsFromData(button) {
                            var bookingId = button.getAttribute('data-booking-id');
                            var roomNumber = button.getAttribute('data-room-number');
                            var customerName = button.getAttribute('data-customer-name');
                            var checkIn = button.getAttribute('data-check-in');
                            var checkOut = button.getAttribute('data-check-out');
                            var totalAmount = button.getAttribute('data-total-amount');
                            var status = button.getAttribute('data-status');

                            document.getElementById('detailBookingId').textContent = '#' + bookingId;
                            document.getElementById('detailRoomNumber').textContent = roomNumber;
                            document.getElementById('detailCustomerName').textContent = customerName;
                            document.getElementById('detailCheckIn').textContent = checkIn;
                            document.getElementById('detailCheckOut').textContent = checkOut;
                            document.getElementById('detailTotalAmount').textContent = parseInt(totalAmount).toLocaleString() + ' VNĐ';

                            var statusElement = document.getElementById('detailStatus');
                            statusElement.textContent = getStatusText(status);
                            statusElement.className = 'badge ' + getStatusBadgeClass(status);

                            var modal = new bootstrap.Modal(document.getElementById('bookingDetailsModal'));
                            modal.show();
                        }

                        function getStatusText(status) {
                            switch (status.toLowerCase()) {
                                case 'confirmed': return 'Đã xác nhận';
                                case 'pending': return 'Chờ xác nhận';
                                case 'cancelled': return 'Đã hủy';
                                case 'completed': return 'Hoàn thành';
                                default: return status;
                            }
                        }

                        function getStatusBadgeClass(status) {
                            switch (status.toLowerCase()) {
                                case 'confirmed': return 'bg-success';
                                case 'pending': return 'bg-warning text-dark';
                                case 'cancelled': return 'bg-danger';
                                case 'completed': return 'bg-info';
                                default: return 'bg-secondary';
                            }
                        }

                        setTimeout(function () {
                            var alerts = document.querySelectorAll('.alert');
                            alerts.forEach(function (alert) {
                                var bsAlert = new bootstrap.Alert(alert);
                                bsAlert.close();
                            });
                        }, 5000);
                    </script>
                </body>

                </html>