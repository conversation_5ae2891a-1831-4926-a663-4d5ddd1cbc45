<%@ Page Language="C#" AutoEventWireup="true" %>
<%@ Import Namespace="QuanLyKhachSan.BLL" %>
<%@ Import Namespace="QuanLyKhachSan.Models" %>

<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON><PERSON> lý kh<PERSON>ch hàng</title>
    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <script runat="server">
        private UserManager userManager;
        private BookingManager bookingManager;
        private List<User> users;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in and is admin
            if (Session["UserId"] == null)
            {
                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }
            
            if (Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("../Default.aspx");
                return;
            }

            userManager = new UserManager();
            bookingManager = new BookingManager();
            LoadUsers();
        }

        private void LoadUsers()
        {
            try
            {
                users = userManager.GetAllUsers().OrderByDescending(u => u.CreatedDate).ToList();
            }
            catch (Exception ex)
            {
                users = new List<User>();
                System.Diagnostics.Debug.WriteLine($"Error loading users: {ex.Message}");
            }
        }

        protected string GetRoleBadgeClass(string role)
        {
            switch (role?.ToLower())
            {
                case "admin":
                    return "bg-danger";
                case "customer":
                    return "bg-primary";
                default:
                    return "bg-secondary";
            }
        }

        protected string GetRoleText(string role)
        {
            switch (role?.ToLower())
            {
                case "admin":
                    return "Quản trị viên";
                case "customer":
                    return "Khách hàng";
                default:
                    return role;
            }
        }

        protected int GetUserBookingCount(int userId)
        {
            try
            {
                var userBookings = bookingManager.GetUserBookings(userId);
                return userBookings.Count;
            }
            catch
            {
                return 0;
            }
        }

        protected decimal GetUserTotalSpent(int userId)
        {
            try
            {
                var userBookings = bookingManager.GetUserBookings(userId);
                return userBookings.Where(b => b.Status == "Confirmed" || b.Status == "Completed").Sum(b => b.TotalAmount);
            }
            catch
            {
                return 0;
            }
        }
    </script>

    <div class="container-fluid mt-4">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="../Default.aspx">
                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="SimpleDashboard.aspx">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                    <a class="nav-link" href="../Default.aspx">
                        <i class="fas fa-home me-1"></i>Trang chủ
                    </a>
                </div>
            </div>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                            <i class="fas fa-users me-3" style="color: var(--accent-blue);"></i>
                            Quản lý khách hàng
                        </h1>
                        <p class="lead" style="color: var(--text-secondary);">
                            Quản lý thông tin người dùng và khách hàng
                        </p>
                    </div>
                    <div>
                        <a href="SimpleDashboard.aspx" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-blue);">
                        <i class="fas fa-users me-2"></i>
                        <%= users?.Count ?? 0 %>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng người dùng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-mint);">
                        <i class="fas fa-user me-2"></i>
                        <%= users?.Where(u => u.Role == "Customer").Count() ?? 0 %>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Khách hàng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-orange);">
                        <i class="fas fa-user-shield me-2"></i>
                        <%= users?.Where(u => u.Role == "Admin").Count() ?? 0 %>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Quản trị viên</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-yellow);">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <%= users?.Where(u => u.CreatedDate.Date == DateTime.Today).Count() ?? 0 %>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Đăng ký hôm nay</p>
                </div>
            </div>
        </div>

        <!-- Users List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2" style="color: var(--accent-blue);"></i>
                            Danh sách người dùng
                        </h5>
                        <div>
                            <button class="btn btn-success" onclick="alert('Chức năng thêm người dùng sẽ được phát triển')">
                                <i class="fas fa-plus me-2"></i>Thêm người dùng
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <% if (users != null && users.Any()) { %>
                            <div class="row g-4">
                                <% foreach (var user in users) { %>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="card h-100 hover-lift">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-user-circle fa-2x me-3" style="color: var(--accent-blue);"></i>
                                                    <div>
                                                        <h6 class="mb-0" style="color: var(--text-primary);"><%= user.Name %></h6>
                                                        <small style="color: var(--text-secondary);"><%= user.Email %></small>
                                                    </div>
                                                </div>
                                                <span class="badge <%= GetRoleBadgeClass(user.Role) %>">
                                                    <%= GetRoleText(user.Role) %>
                                                </span>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <div class="text-center">
                                                            <h5 style="color: var(--accent-mint);">
                                                                <%= GetUserBookingCount(user.UserId) %>
                                                            </h5>
                                                            <small style="color: var(--text-secondary);">Đặt phòng</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="text-center">
                                                            <h5 style="color: var(--accent-orange);">
                                                                <%= GetUserTotalSpent(user.UserId).ToString("N0") %>
                                                            </h5>
                                                            <small style="color: var(--text-secondary);">Tổng chi (VNĐ)</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small style="color: var(--text-muted);">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        Tham gia: <%= user.CreatedDate.ToString("dd/MM/yyyy") %>
                                                    </small>
                                                    <small style="color: var(--text-muted);">
                                                        ID: #<%= user.UserId %>
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-transparent border-0 p-3">
                                                <div class="d-grid gap-2">
                                                    <button class="btn btn-outline-primary btn-sm" 
                                                            onclick="alert('Xem chi tiết người dùng <%= user.Name %> (ID: <%= user.UserId %>)')">
                                                        <i class="fas fa-eye me-2"></i>Xem chi tiết
                                                    </button>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-outline-success btn-sm" 
                                                                onclick="alert('Chỉnh sửa thông tin <%= user.Name %>')">
                                                            <i class="fas fa-edit me-1"></i>Sửa
                                                        </button>
                                                        <% if (user.Role != "Admin") { %>
                                                            <button class="btn btn-outline-warning btn-sm" 
                                                                    onclick="alert('Thay đổi quyền <%= user.Name %>')">
                                                                <i class="fas fa-user-cog me-1"></i>Quyền
                                                            </button>
                                                            <button class="btn btn-outline-danger btn-sm" 
                                                                    onclick="alert('Vô hiệu hóa tài khoản <%= user.Name %>')">
                                                                <i class="fas fa-ban me-1"></i>Khóa
                                                            </button>
                                                        <% } %>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <% } %>
                            </div>
                        <% } else { %>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-4x mb-4" style="color: var(--text-muted);"></i>
                                <h4 style="color: var(--text-secondary);">Không có người dùng nào</h4>
                                <p style="color: var(--text-muted);">Hệ thống chưa có dữ liệu người dùng hoặc có lỗi kết nối database.</p>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2" style="color: var(--accent-orange);"></i>
                            Thao tác nhanh
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-lg-3 col-md-6">
                                <a href="ManageRooms.aspx" class="btn btn-primary w-100">
                                    <i class="fas fa-bed me-2"></i>Quản lý phòng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="ManageBookings.aspx" class="btn btn-success w-100">
                                    <i class="fas fa-calendar-check me-2"></i>Quản lý đặt phòng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="../Register.aspx" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-user-plus me-2"></i>Trang đăng ký
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="SimpleDashboard.aspx" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../Scripts/bootstrap.bundle.min.js"></script>
</body>
</html>
