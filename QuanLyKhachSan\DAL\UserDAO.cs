using System;
using System.Collections.Generic;
using MySql.Data.MySqlClient;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan.DAL
{
    public class UserDAO
    {
        public bool CreateUser(User user)
        {
            try
            {
                string query = @"INSERT INTO Users (Name, Email, PasswordHash, Role, CreatedDate) 
                               VALUES (@Name, @Email, @PasswordHash, @Role, @CreatedDate)";

                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@Name", user.Name),
                    new MySqlParameter("@Email", user.Email),
                    new MySqlParameter("@PasswordHash", user.PasswordHash),
                    new MySqlParameter("@Role", user.Role),
                    new MySqlParameter("@CreatedDate", user.CreatedDate)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating user: {ex.Message}");
                return false;
            }
        }

        public User GetUserByEmail(string email)
        {
            try
            {
                string query = "SELECT UserId, Name, Email, PasswordHash, Role, CreatedDate FROM Users WHERE Email = @Email";
                
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var reader = DatabaseHelper.ExecuteReader(query, connection, new MySqlParameter("@Email", email)))
                    {
                        if (reader.Read())
                        {
                            return new User
                            {
                                UserId = reader.GetInt32("UserId"),
                                Name = reader.GetString("Name"),
                                Email = reader.GetString("Email"),
                                PasswordHash = reader.GetString("PasswordHash"),
                                Role = reader.GetString("Role"),
                                CreatedDate = reader.GetDateTime("CreatedDate")
                            };
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting user by email: {ex.Message}");
                return null;
            }
        }

        public User GetUserById(int userId)
        {
            try
            {
                string query = "SELECT UserId, Name, Email, PasswordHash, Role, CreatedDate FROM Users WHERE UserId = @UserId";
                
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var reader = DatabaseHelper.ExecuteReader(query, connection, new MySqlParameter("@UserId", userId)))
                    {
                        if (reader.Read())
                        {
                            return new User
                            {
                                UserId = reader.GetInt32("UserId"),
                                Name = reader.GetString("Name"),
                                Email = reader.GetString("Email"),
                                PasswordHash = reader.GetString("PasswordHash"),
                                Role = reader.GetString("Role"),
                                CreatedDate = reader.GetDateTime("CreatedDate")
                            };
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting user by ID: {ex.Message}");
                return null;
            }
        }

        public bool EmailExists(string email)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Users WHERE Email = @Email";
                var result = DatabaseHelper.ExecuteScalar(query, new MySqlParameter("@Email", email));
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking email existence: {ex.Message}");
                return false;
            }
        }

        public bool UpdateUser(User user)
        {
            try
            {
                string query = @"UPDATE Users SET Name = @Name, Email = @Email, Role = @Role 
                               WHERE UserId = @UserId";

                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@Name", user.Name),
                    new MySqlParameter("@Email", user.Email),
                    new MySqlParameter("@Role", user.Role),
                    new MySqlParameter("@UserId", user.UserId)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating user: {ex.Message}");
                return false;
            }
        }
    }
}
