-- <PERSON>ript để cập nhật triggers cho logic booking mới
-- Chạy script này để sửa logic room availability

-- <PERSON><PERSON><PERSON> triggers cũ
DROP TRIGGER IF EXISTS UpdateRoomAvailability;
DROP TRIGGER IF EXISTS UpdateRoomAvailabilityOnCancel;

-- Tạo trigger mới: Chỉ cập nhật room availability khi booking được CONFIRMED
DELIMITER //
CREATE TRIGGER UpdateRoomAvailabilityOnConfirm 
AFTER UPDATE ON Bookings
FOR EACH ROW
BEGIN
    -- <PERSON>hi booking chuyển từ Pending sang Confirmed
    IF OLD.Status = 'Pending' AND NEW.Status = 'Confirmed' THEN
        UPDATE Rooms 
        SET IsAvailable = FALSE 
        WHERE RoomId = NEW.RoomId;
    END IF;
    
    -- Khi booking chuyển từ Confirmed sang Completed
    IF OLD.Status = 'Confirmed' AND NEW.Status = 'Completed' THEN
        -- Kiểm tra xem có booking nào khác đang Confirmed cho phòng này không
        IF NOT EXISTS (
            SELECT 1 FROM Bookings 
            WHERE RoomId = NEW.RoomId 
            AND Status = 'Confirmed'
            AND BookingId != NEW.BookingId
            AND CheckOutDate > CURDATE()
        ) THEN
            UPDATE Rooms 
            SET IsAvailable = TRUE 
            WHERE RoomId = NEW.RoomId;
        END IF;
    END IF;
    
    -- Khi booking bị hủy (từ Pending hoặc Confirmed)
    IF (OLD.Status = 'Pending' OR OLD.Status = 'Confirmed') AND NEW.Status = 'Cancelled' THEN
        -- Nếu booking cũ là Confirmed, cần kiểm tra và có thể set room available
        IF OLD.Status = 'Confirmed' THEN
            -- Kiểm tra xem có booking nào khác đang Confirmed cho phòng này không
            IF NOT EXISTS (
                SELECT 1 FROM Bookings 
                WHERE RoomId = NEW.RoomId 
                AND Status = 'Confirmed'
                AND BookingId != NEW.BookingId
                AND CheckOutDate > CURDATE()
            ) THEN
                UPDATE Rooms 
                SET IsAvailable = TRUE 
                WHERE RoomId = NEW.RoomId;
            END IF;
        END IF;
    END IF;
END //
DELIMITER ;

-- Tạo trigger để tự động set BookingDate khi tạo booking mới
DELIMITER //
CREATE TRIGGER SetBookingDate 
BEFORE INSERT ON Bookings
FOR EACH ROW
BEGIN
    IF NEW.BookingDate IS NULL THEN
        SET NEW.BookingDate = NOW();
    END IF;
END //
DELIMITER ;

-- Cập nhật tất cả phòng về trạng thái available trước
UPDATE Rooms SET IsAvailable = TRUE;

-- Sau đó set lại availability dựa trên bookings hiện tại
UPDATE Rooms r
SET IsAvailable = FALSE
WHERE EXISTS (
    SELECT 1 FROM Bookings b
    WHERE b.RoomId = r.RoomId
    AND b.Status = 'Confirmed'
    AND b.CheckOutDate > CURDATE()
);

-- Hiển thị kết quả
SELECT 'Updated Triggers Successfully' as Result;
SELECT 'Current Room Availability:' as Info;
SELECT RoomId, RoomNumber, RoomType, IsAvailable FROM Rooms ORDER BY RoomNumber;

SELECT 'Current Active Bookings:' as Info;
SELECT BookingId, RoomId, Status, CheckInDate, CheckOutDate, CustomerName 
FROM Bookings 
WHERE Status IN ('Pending', 'Confirmed') 
ORDER BY BookingDate DESC;
