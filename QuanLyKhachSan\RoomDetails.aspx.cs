using System;
using System.Web.UI;
using QuanLyKhachSan.BLL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan
{
    public partial class RoomDetails : Page
    {
        private RoomManager roomManager;
        private int roomId;

        protected void Page_Load(object sender, EventArgs e)
        {
            roomManager = new RoomManager();

            // Get room ID from query string
            if (!int.TryParse(Request.QueryString["roomId"], out roomId))
            {
                ShowRoomNotFound();
                return;
            }

            if (!IsPostBack)
            {
                LoadRoomDetails();
                SetDefaultDates();
            }
        }

        private void LoadRoomDetails()
        {
            try
            {
                var room = roomManager.GetRoomById(roomId);
                if (room == null)
                {
                    ShowRoomNotFound();
                    return;
                }

                // Display room information
                lblRoomNumber.Text = room.RoomNumber;
                lblRoomType.Text = room.RoomType;
                lblPrice.Text = string.Format("{0:N0}", room.Price);
                lblMaxOccupancy.Text = room.MaxOccupancy.ToString();
                lblDescription.Text = string.IsNullOrEmpty(room.Description) ? "Không có mô tả" : room.Description;

                // Set availability status
                if (room.IsAvailable)
                {
                    lblStatus.Text = "Có sẵn";
                    lblAvailability.Text = "Có sẵn";
                    statusBadge.Attributes["class"] = "badge badge-success";
                    btnBookRoom.Enabled = true;
                }
                else
                {
                    lblStatus.Text = "Đã đặt";
                    lblAvailability.Text = "Đã đặt";
                    statusBadge.Attributes["class"] = "badge badge-danger";
                    btnBookRoom.Enabled = false;
                }

                // Set room image
                if (!string.IsNullOrEmpty(room.ImageUrl))
                {
                    imgRoom.ImageUrl = room.ImageUrl;
                }
                else
                {
                    imgRoom.ImageUrl = "~/Content/images/default-room.jpg"; // Default image
                }

                pnlRoomDetails.Visible = true;
                pnlRoomNotFound.Visible = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading room details: {ex.Message}");
                ShowMessage("Có lỗi xảy ra khi tải thông tin phòng.", "alert-danger");
            }
        }

        private void SetDefaultDates()
        {
            // Get dates from query string if available
            string checkInParam = Request.QueryString["checkIn"];
            string checkOutParam = Request.QueryString["checkOut"];

            if (!string.IsNullOrEmpty(checkInParam) && DateTime.TryParse(checkInParam, out DateTime checkIn))
            {
                txtCheckIn.Text = checkIn.ToString("yyyy-MM-dd");
            }
            else
            {
                txtCheckIn.Text = DateTime.Today.ToString("yyyy-MM-dd");
            }

            if (!string.IsNullOrEmpty(checkOutParam) && DateTime.TryParse(checkOutParam, out DateTime checkOut))
            {
                txtCheckOut.Text = checkOut.ToString("yyyy-MM-dd");
            }
            else
            {
                txtCheckOut.Text = DateTime.Today.AddDays(1).ToString("yyyy-MM-dd");
            }

            // Calculate initial total amount
            CalculateTotalAmount();
        }

        protected void btnCalculate_Click(object sender, EventArgs e)
        {
            CalculateTotalAmount();
        }

        private void CalculateTotalAmount()
        {
            try
            {
                if (DateTime.TryParse(txtCheckIn.Text, out DateTime checkIn) && 
                    DateTime.TryParse(txtCheckOut.Text, out DateTime checkOut))
                {
                    if (checkOut <= checkIn)
                    {
                        lblTotalAmount.Text = "0 VNĐ";
                        ShowMessage("Ngày trả phòng phải sau ngày nhận phòng.", "alert-warning");
                        return;
                    }

                    decimal totalAmount = roomManager.CalculateRoomPrice(roomId, checkIn, checkOut);
                    lblTotalAmount.Text = string.Format("{0:N0} VNĐ", totalAmount);
                    pnlMessage.Visible = false;
                }
                else
                {
                    lblTotalAmount.Text = "0 VNĐ";
                    ShowMessage("Vui lòng chọn ngày hợp lệ.", "alert-warning");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating total amount: {ex.Message}");
                lblTotalAmount.Text = "0 VNĐ";
                ShowMessage("Có lỗi xảy ra khi tính toán giá phòng.", "alert-danger");
            }
        }

        protected void btnBookRoom_Click(object sender, EventArgs e)
        {
            // Check if user is logged in
            if (Session["UserId"] == null)
            {
                string returnUrl = $"RoomDetails.aspx?roomId={roomId}&checkIn={txtCheckIn.Text}&checkOut={txtCheckOut.Text}";
                Response.Redirect($"Login.aspx?ReturnUrl={Server.UrlEncode(returnUrl)}");
                return;
            }

            // Validate dates
            if (!DateTime.TryParse(txtCheckIn.Text, out DateTime checkIn) || 
                !DateTime.TryParse(txtCheckOut.Text, out DateTime checkOut))
            {
                ShowMessage("Vui lòng chọn ngày hợp lệ.", "alert-warning");
                return;
            }

            // Redirect to booking page
            Response.Redirect($"BookRoom.aspx?roomId={roomId}&checkIn={txtCheckIn.Text}&checkOut={txtCheckOut.Text}");
        }

        private void ShowRoomNotFound()
        {
            pnlRoomDetails.Visible = false;
            pnlRoomNotFound.Visible = true;
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }
    }
}
