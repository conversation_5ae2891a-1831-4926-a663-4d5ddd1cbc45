<%@ Page Title="Admin Dashboard" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Dashboard.aspx.cs" Inherits="QuanLyKhachSan.Admin.Dashboard" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                            <i class="fas fa-tachometer-alt me-3" style="color: var(--accent-blue);"></i>
                            Admin Dashboard
                        </h1>
                        <p class="lead" style="color: var(--text-secondary);">
                            Quản lý hệ thống khách sạn
                        </p>
                    </div>
                    <div>
                        <span class="badge bg-success fs-6 px-3 py-2">
                            <i class="fas fa-user-shield me-2"></i>Administrator
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-5">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-blue);">
                        <i class="fas fa-bed me-2"></i>
                        <asp:Label ID="lblTotalRooms" runat="server" Text="0"></asp:Label>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng số phòng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-mint);">
                        <i class="fas fa-calendar-check me-2"></i>
                        <asp:Label ID="lblTotalBookings" runat="server" Text="0"></asp:Label>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng đặt phòng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-orange);">
                        <i class="fas fa-users me-2"></i>
                        <asp:Label ID="lblTotalUsers" runat="server" Text="0"></asp:Label>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng khách hàng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-yellow);">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        <asp:Label ID="lblTotalRevenue" runat="server" Text="0"></asp:Label>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Doanh thu (VNĐ)</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2" style="color: var(--accent-blue);"></i>
                            Thao tác nhanh
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-lg-3 col-md-6">
                                <a href="ManageRooms.aspx" class="btn btn-primary w-100 py-3">
                                    <i class="fas fa-bed me-2"></i>Quản lý phòng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="ManageBookings.aspx" class="btn btn-success w-100 py-3">
                                    <i class="fas fa-calendar-alt me-2"></i>Quản lý đặt phòng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="ManageUsers.aspx" class="btn btn-info w-100 py-3">
                                    <i class="fas fa-users me-2"></i>Quản lý người dùng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="Reports.aspx" class="btn btn-warning w-100 py-3">
                                    <i class="fas fa-chart-bar me-2"></i>Báo cáo
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2" style="color: var(--accent-mint);"></i>
                            Đặt phòng gần đây
                        </h5>
                        <a href="ManageBookings.aspx" class="btn btn-outline-primary btn-sm">
                            Xem tất cả
                        </a>
                    </div>
                    <div class="card-body">
                        <asp:GridView ID="gvRecentBookings" runat="server" CssClass="table table-hover" 
                            AutoGenerateColumns="false" EmptyDataText="Chưa có đặt phòng nào">
                            <Columns>
                                <asp:BoundField DataField="BookingId" HeaderText="ID" />
                                <asp:BoundField DataField="CustomerName" HeaderText="Khách hàng" />
                                <asp:BoundField DataField="RoomNumber" HeaderText="Phòng" />
                                <asp:BoundField DataField="CheckInDate" HeaderText="Ngày nhận" DataFormatString="{0:dd/MM/yyyy}" />
                                <asp:BoundField DataField="CheckOutDate" HeaderText="Ngày trả" DataFormatString="{0:dd/MM/yyyy}" />
                                <asp:TemplateField HeaderText="Trạng thái">
                                    <ItemTemplate>
                                        <span class="badge <%# GetStatusBadgeClass(Eval("Status").ToString()) %>">
                                            <%# Eval("Status") %>
                                        </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="TotalAmount" HeaderText="Tổng tiền" DataFormatString="{0:N0} VNĐ" />
                            </Columns>
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </div>

        <!-- Room Status Overview -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-door-open me-2" style="color: var(--accent-orange);"></i>
                            Tình trạng phòng
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="mb-3">
                                    <h3 style="color: var(--accent-mint);">
                                        <asp:Label ID="lblAvailableRooms" runat="server" Text="0"></asp:Label>
                                    </h3>
                                    <p style="color: var(--text-secondary);">Phòng trống</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mb-3">
                                    <h3 style="color: var(--danger);">
                                        <asp:Label ID="lblOccupiedRooms" runat="server" Text="0"></asp:Label>
                                    </h3>
                                    <p style="color: var(--text-secondary);">Phòng đã đặt</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2" style="color: var(--accent-purple);"></i>
                            Thống kê hôm nay
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="mb-3">
                                    <h3 style="color: var(--accent-blue);">
                                        <asp:Label ID="lblTodayBookings" runat="server" Text="0"></asp:Label>
                                    </h3>
                                    <p style="color: var(--text-secondary);">Đặt phòng hôm nay</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mb-3">
                                    <h3 style="color: var(--accent-yellow);">
                                        <asp:Label ID="lblTodayRevenue" runat="server" Text="0"></asp:Label>
                                    </h3>
                                    <p style="color: var(--text-secondary);">Doanh thu hôm nay</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
