<!-- Admin Navigation Component -->
<nav class="navbar navbar-expand-lg navbar-dark mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
    <div class="container-fluid">
        <a class="navbar-brand" href="../Default.aspx">
            <i class="fas fa-hotel me-2"></i>Khách sạn ABC
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar" aria-controls="adminNavbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="adminNavbar">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="WorkingDashboard.aspx" id="nav-dashboard">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="ManageRoomsAdvanced.aspx" id="nav-rooms">
                        <i class="fas fa-bed me-1"></i>Quản lý phòng
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="ManageBookingsAdvanced.aspx" id="nav-bookings">
                        <i class="fas fa-calendar-check me-1"></i>Quản lý đặt phòng
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="ManageUsers.aspx" id="nav-users">
                        <i class="fas fa-users me-1"></i>Quản lý khách hàng
                    </a>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-shield me-1"></i>Admin
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="DatabaseSetup.aspx">
                                <i class="fas fa-database me-2"></i>Database Setup
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="TestAllFeatures.aspx">
                                <i class="fas fa-vial me-2"></i>Test Features
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="../Default.aspx">
                                <i class="fas fa-home me-2"></i>Trang chủ
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="../Login.aspx?action=logout">
                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<script>
// Set active navigation item based on current page
document.addEventListener('DOMContentLoaded', function() {
    const currentPage = window.location.pathname.toLowerCase();
    
    // Remove active class from all nav links
    document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Add active class to current page
    if (currentPage.includes('workingdashboard')) {
        document.getElementById('nav-dashboard')?.classList.add('active');
    } else if (currentPage.includes('managerooms')) {
        document.getElementById('nav-rooms')?.classList.add('active');
    } else if (currentPage.includes('managebookings')) {
        document.getElementById('nav-bookings')?.classList.add('active');
    } else if (currentPage.includes('manageusers')) {
        document.getElementById('nav-users')?.classList.add('active');
    }
});
</script>

<style>
.navbar-nav .nav-link.active {
    background-color: rgba(255,255,255,0.2);
    border-radius: 5px;
    font-weight: 600;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    border-radius: 5px;
    transition: all 0.3s ease;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.dropdown-item:hover {
    background-color: var(--accent-blue);
    color: white;
}
</style>
