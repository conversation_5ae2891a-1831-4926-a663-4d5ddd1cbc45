using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using QuanLyKhachSan.BLL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan.Admin
{
    public partial class Dashboard : System.Web.UI.Page
    {
        private RoomManager roomManager;
        private BookingManager bookingManager;
        private UserManager userManager;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in and is admin
            if (Session["UserId"] == null || Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("~/Login.aspx");
                return;
            }

            roomManager = new RoomManager();
            bookingManager = new BookingManager();
            userManager = new UserManager();

            if (!IsPostBack)
            {
                LoadDashboardData();
                LoadRecentBookings();
            }
        }

        private void LoadDashboardData()
        {
            try
            {
                // Get all rooms
                var allRooms = roomManager.GetAllRooms();
                lblTotalRooms.Text = allRooms.Count.ToString();

                // Count available and occupied rooms
                var availableRooms = allRooms.Where(r => r.IsAvailable).Count();
                var occupiedRooms = allRooms.Count - availableRooms;
                lblAvailableRooms.Text = availableRooms.ToString();
                lblOccupiedRooms.Text = occupiedRooms.ToString();

                // Get all bookings
                var allBookings = bookingManager.GetAllBookings();
                lblTotalBookings.Text = allBookings.Count.ToString();

                // Calculate total revenue
                var totalRevenue = allBookings
                    .Where(b => b.Status == "Confirmed" || b.Status == "Completed")
                    .Sum(b => b.TotalAmount);
                lblTotalRevenue.Text = totalRevenue.ToString("N0");

                // Get all users (customers only)
                var allUsers = userManager.GetAllUsers();
                var customerCount = allUsers.Where(u => u.Role == "Customer").Count();
                lblTotalUsers.Text = customerCount.ToString();

                // Today's statistics
                var today = DateTime.Today;
                var todayBookings = allBookings.Where(b => b.BookingDate.Date == today).Count();
                lblTodayBookings.Text = todayBookings.ToString();

                var todayRevenue = allBookings
                    .Where(b => b.BookingDate.Date == today && (b.Status == "Confirmed" || b.Status == "Completed"))
                    .Sum(b => b.TotalAmount);
                lblTodayRevenue.Text = todayRevenue.ToString("N0");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard data: {ex.Message}");
            }
        }

        private void LoadRecentBookings()
        {
            try
            {
                var recentBookings = bookingManager.GetRecentBookings(10);
                gvRecentBookings.DataSource = recentBookings;
                gvRecentBookings.DataBind();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading recent bookings: {ex.Message}");
            }
        }

        protected string GetStatusBadgeClass(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "bg-success";
                case "pending":
                    return "bg-warning";
                case "cancelled":
                    return "bg-danger";
                case "completed":
                    return "bg-info";
                default:
                    return "bg-secondary";
            }
        }
    }
}
