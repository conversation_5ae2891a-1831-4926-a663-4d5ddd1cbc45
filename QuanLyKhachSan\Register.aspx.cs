using System;
using System.Web.UI;
using QuanLyKhachSan.BLL;

namespace QuanLyKhachSan
{
    public partial class Register : Page
    {
        private UserManager userManager;

        protected void Page_Load(object sender, EventArgs e)
        {
            userManager = new UserManager();

            // If user is already logged in, redirect to home page
            if (Session["UserId"] != null)
            {
                Response.Redirect("Default.aspx");
            }
        }

        protected void btnRegister_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                try
                {
                    string name = txtName.Text.Trim();
                    string email = txtEmail.Text.Trim();
                    string password = txtPassword.Text;
                    string confirmPassword = txtConfirmPassword.Text;

                    // Validate registration data
                    string validationMessage = userManager.ValidateRegistrationData(name, email, password, confirmPassword);
                    
                    if (!string.IsNullOrEmpty(validationMessage))
                    {
                        ShowMessage(validationMessage, "alert-danger");
                        return;
                    }

                    // Attempt to register user
                    bool success = userManager.RegisterUser(name, email, password);

                    if (success)
                    {
                        ShowMessage("Đăng ký thành công! Bạn có thể đăng nhập ngay bây giờ.", "alert-success");
                        ClearForm();
                        
                        // Redirect to login page after a short delay
                        Response.AddHeader("REFRESH", "2;URL=Login.aspx");
                    }
                    else
                    {
                        ShowMessage("Đăng ký thất bại. Vui lòng thử lại.", "alert-danger");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Registration error: {ex.Message}");
                    ShowMessage("Có lỗi xảy ra trong quá trình đăng ký. Vui lòng thử lại.", "alert-danger");
                }
            }
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }

        private void ClearForm()
        {
            txtName.Text = "";
            txtEmail.Text = "";
            txtPassword.Text = "";
            txtConfirmPassword.Text = "";
        }
    }
}
