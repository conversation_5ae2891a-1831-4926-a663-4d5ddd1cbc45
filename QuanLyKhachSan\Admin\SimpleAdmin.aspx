<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Admin Page</title>
    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <script runat="server">
        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in
            if (Session["UserId"] == null)
            {
                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }
            
            // Check if user is admin
            if (Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("../Default.aspx");
                return;
            }
        }
    </script>

    <div class="container-fluid mt-4">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="../Default.aspx">
                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="../Default.aspx">
                        <i class="fas fa-home me-1"></i>Trang chủ
                    </a>
                    <a class="nav-link" href="../Test.aspx">
                        <i class="fas fa-vial me-1"></i>Test
                    </a>
                </div>
            </div>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                            <i class="fas fa-tachometer-alt me-3" style="color: var(--accent-blue);"></i>
                            Simple Admin Dashboard
                        </h1>
                        <p class="lead" style="color: var(--text-secondary);">
                            Quản lý hệ thống khách sạn
                        </p>
                    </div>
                    <div>
                        <span class="badge bg-success fs-6 px-3 py-2">
                            <i class="fas fa-user-shield me-2"></i>Administrator
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            <strong>Thành công!</strong> Bạn đã truy cập được trang Admin.
        </div>

        <!-- Session Information -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2" style="color: var(--accent-blue);"></i>
                            Thông tin Session
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>User ID:</strong><br>
                                <span class="text-muted"><%= Session["UserId"] ?? "N/A" %></span>
                            </div>
                            <div class="col-md-4">
                                <strong>User Name:</strong><br>
                                <span class="text-muted"><%= Session["UserName"] ?? "N/A" %></span>
                            </div>
                            <div class="col-md-4">
                                <strong>User Role:</strong><br>
                                <span class="badge bg-primary"><%= Session["UserRole"] ?? "N/A" %></span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Session ID:</strong><br>
                                <small class="text-muted"><%= Session.SessionID %></small>
                            </div>
                            <div class="col-md-6">
                                <strong>Server Time:</strong><br>
                                <span class="text-muted"><%= DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") %></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-5">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-blue);">
                        <i class="fas fa-bed me-2"></i>10
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng số phòng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-mint);">
                        <i class="fas fa-calendar-check me-2"></i>25
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng đặt phòng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-orange);">
                        <i class="fas fa-users me-2"></i>15
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng khách hàng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-yellow);">
                        <i class="fas fa-money-bill-wave me-2"></i>50M
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Doanh thu (VNĐ)</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2" style="color: var(--accent-blue);"></i>
                            Thao tác nhanh
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-lg-3 col-md-6">
                                <a href="../RoomList.aspx" class="btn btn-primary w-100 py-3">
                                    <i class="fas fa-bed me-2"></i>Xem danh sách phòng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="../Default.aspx" class="btn btn-success w-100 py-3">
                                    <i class="fas fa-home me-2"></i>Trang chủ
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="../Test.aspx" class="btn btn-info w-100 py-3">
                                    <i class="fas fa-vial me-2"></i>Test Page
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="AdminTest.aspx" class="btn btn-warning w-100 py-3">
                                    <i class="fas fa-cog me-2"></i>Admin Test
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2" style="color: var(--accent-purple);"></i>
                            Trạng thái hệ thống
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Hệ thống đang hoạt động bình thường
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-database me-2"></i>
                            Database kết nối thành công
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2" style="color: var(--accent-orange);"></i>
                            Công cụ quản lý
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="../RoomList.aspx" class="btn btn-outline-primary">
                                <i class="fas fa-bed me-2"></i>Quản lý phòng
                            </a>
                            <a href="../MyBookings.aspx" class="btn btn-outline-success">
                                <i class="fas fa-calendar-check me-2"></i>Quản lý đặt phòng
                            </a>
                            <a href="../Login.aspx" class="btn btn-outline-secondary">
                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../Scripts/bootstrap.bundle.min.js"></script>
</body>
</html>
