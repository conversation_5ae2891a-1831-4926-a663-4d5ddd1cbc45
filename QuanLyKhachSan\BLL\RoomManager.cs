using System;
using System.Collections.Generic;
using System.Linq;
using QuanLyKhachSan.DAL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan.BLL
{
    public class RoomManager
    {
        private RoomDAO roomDAO;

        public RoomManager()
        {
            roomDAO = new RoomDAO();
        }

        public List<Room> GetAllRooms()
        {
            try
            {
                return roomDAO.GetAllRooms();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetAllRooms: {ex.Message}");
                return new List<Room>();
            }
        }

        public List<Room> GetAvailableRooms()
        {
            try
            {
                return roomDAO.GetAvailableRooms();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetAvailableRooms: {ex.Message}");
                return new List<Room>();
            }
        }

        public Room GetRoomById(int roomId)
        {
            try
            {
                return roomDAO.GetRoomById(roomId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetRoomById: {ex.Message}");
                return null;
            }
        }

        public bool IsRoomAvailable(int roomId, DateTime checkIn, DateTime checkOut)
        {
            try
            {
                // Validate dates
                if (checkIn >= checkOut)
                {
                    return false;
                }

                if (checkIn.Date < DateTime.Today)
                {
                    return false;
                }

                return roomDAO.IsRoomAvailable(roomId, checkIn, checkOut);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in IsRoomAvailable: {ex.Message}");
                return false;
            }
        }

        public List<Room> GetAvailableRoomsForDates(DateTime checkIn, DateTime checkOut)
        {
            try
            {
                var allRooms = GetAvailableRooms();
                var availableRooms = new List<Room>();

                foreach (var room in allRooms)
                {
                    if (IsRoomAvailable(room.RoomId, checkIn, checkOut))
                    {
                        availableRooms.Add(room);
                    }
                }

                return availableRooms;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetAvailableRoomsForDates: {ex.Message}");
                return new List<Room>();
            }
        }

        public List<Room> SearchRooms(string roomType = "", decimal? minPrice = null, decimal? maxPrice = null)
        {
            try
            {
                var rooms = GetAvailableRooms();

                if (!string.IsNullOrWhiteSpace(roomType))
                {
                    rooms = rooms.Where(r => r.RoomType.ToLower().Contains(roomType.ToLower())).ToList();
                }

                if (minPrice.HasValue)
                {
                    rooms = rooms.Where(r => r.Price >= minPrice.Value).ToList();
                }

                if (maxPrice.HasValue)
                {
                    rooms = rooms.Where(r => r.Price <= maxPrice.Value).ToList();
                }

                return rooms;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in SearchRooms: {ex.Message}");
                return new List<Room>();
            }
        }

        public bool UpdateRoomAvailability(int roomId, bool isAvailable)
        {
            try
            {
                return roomDAO.UpdateRoomAvailability(roomId, isAvailable);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateRoomAvailability: {ex.Message}");
                return false;
            }
        }

        public decimal CalculateRoomPrice(int roomId, DateTime checkIn, DateTime checkOut)
        {
            try
            {
                var room = GetRoomById(roomId);
                if (room == null)
                {
                    return 0;
                }

                int numberOfNights = (checkOut - checkIn).Days;
                if (numberOfNights <= 0)
                {
                    return 0;
                }

                return room.Price * numberOfNights;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CalculateRoomPrice: {ex.Message}");
                return 0;
            }
        }

        public string ValidateBookingDates(DateTime checkIn, DateTime checkOut)
        {
            if (checkIn.Date < DateTime.Today)
            {
                return "Ngày nhận phòng không thể là ngày trong quá khứ.";
            }

            if (checkOut.Date <= checkIn.Date)
            {
                return "Ngày trả phòng phải sau ngày nhận phòng.";
            }

            if ((checkOut - checkIn).Days > 30)
            {
                return "Thời gian đặt phòng không được quá 30 ngày.";
            }

            return string.Empty; // No errors
        }

        public List<string> GetAvailableRoomTypes()
        {
            try
            {
                var rooms = GetAllRooms();
                return rooms.Select(r => r.RoomType).Distinct().OrderBy(t => t).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetAvailableRoomTypes: {ex.Message}");
                return new List<string>();
            }
        }

        public Dictionary<string, int> GetRoomStatistics()
        {
            try
            {
                var rooms = GetAllRooms();
                return new Dictionary<string, int>
                {
                    ["TotalRooms"] = rooms.Count,
                    ["AvailableRooms"] = rooms.Count(r => r.IsAvailable),
                    ["OccupiedRooms"] = rooms.Count(r => !r.IsAvailable)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetRoomStatistics: {ex.Message}");
                return new Dictionary<string, int>();
            }
        }
    }
}
