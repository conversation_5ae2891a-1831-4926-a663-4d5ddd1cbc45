using System;
using System.Collections.Generic;
using MySql.Data.MySqlClient;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan.DAL
{
    public class RoomDAO
    {
        public List<Room> GetAllRooms()
        {
            var rooms = new List<Room>();
            try
            {
                string query = @"SELECT RoomId, RoomNumber, RoomType, Price, Description, 
                               IsAvailable, ImageUrl, MaxOccupancy FROM Rooms ORDER BY RoomNumber";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var reader = DatabaseHelper.ExecuteReader(query, connection))
                    {
                        while (reader.Read())
                        {
                            rooms.Add(new Room
                            {
                                RoomId = reader.GetInt32("RoomId"),
                                RoomNumber = reader.GetString("RoomNumber"),
                                RoomType = reader.GetString("RoomType"),
                                Price = reader.GetDecimal("Price"),
                                Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? "" : reader.GetString(reader.GetOrdinal("Description")),
                                IsAvailable = reader.GetBoolean("IsAvailable"),
                                ImageUrl = reader.IsDBNull(reader.GetOrdinal("ImageUrl")) ? "" : reader.GetString(reader.GetOrdinal("ImageUrl")),
                                MaxOccupancy = reader.GetInt32("MaxOccupancy")
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting all rooms: {ex.Message}");
            }
            return rooms;
        }

        public List<Room> GetAvailableRooms()
        {
            var rooms = new List<Room>();
            try
            {
                string query = @"SELECT RoomId, RoomNumber, RoomType, Price, Description, 
                               IsAvailable, ImageUrl, MaxOccupancy FROM Rooms 
                               WHERE IsAvailable = 1 ORDER BY RoomNumber";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var reader = DatabaseHelper.ExecuteReader(query, connection))
                    {
                        while (reader.Read())
                        {
                            rooms.Add(new Room
                            {
                                RoomId = reader.GetInt32("RoomId"),
                                RoomNumber = reader.GetString("RoomNumber"),
                                RoomType = reader.GetString("RoomType"),
                                Price = reader.GetDecimal("Price"),
                                Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? "" : reader.GetString(reader.GetOrdinal("Description")),
                                IsAvailable = reader.GetBoolean("IsAvailable"),
                                ImageUrl = reader.IsDBNull(reader.GetOrdinal("ImageUrl")) ? "" : reader.GetString(reader.GetOrdinal("ImageUrl")),
                                MaxOccupancy = reader.GetInt32("MaxOccupancy")
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting available rooms: {ex.Message}");
            }
            return rooms;
        }

        public Room GetRoomById(int roomId)
        {
            try
            {
                string query = @"SELECT RoomId, RoomNumber, RoomType, Price, Description, 
                               IsAvailable, ImageUrl, MaxOccupancy FROM Rooms WHERE RoomId = @RoomId";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var reader = DatabaseHelper.ExecuteReader(query, connection, new MySqlParameter("@RoomId", roomId)))
                    {
                        if (reader.Read())
                        {
                            return new Room
                            {
                                RoomId = reader.GetInt32("RoomId"),
                                RoomNumber = reader.GetString("RoomNumber"),
                                RoomType = reader.GetString("RoomType"),
                                Price = reader.GetDecimal("Price"),
                                Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? "" : reader.GetString(reader.GetOrdinal("Description")),
                                IsAvailable = reader.GetBoolean("IsAvailable"),
                                ImageUrl = reader.IsDBNull(reader.GetOrdinal("ImageUrl")) ? "" : reader.GetString(reader.GetOrdinal("ImageUrl")),
                                MaxOccupancy = reader.GetInt32("MaxOccupancy")
                            };
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting room by ID: {ex.Message}");
                return null;
            }
        }

        public bool IsRoomAvailable(int roomId, DateTime checkIn, DateTime checkOut)
        {
            try
            {
                string query = @"SELECT COUNT(*) FROM Bookings 
                               WHERE RoomId = @RoomId 
                               AND Status IN ('Confirmed', 'Pending')
                               AND (
                                   (CheckInDate <= @CheckIn AND CheckOutDate > @CheckIn) OR
                                   (CheckInDate < @CheckOut AND CheckOutDate >= @CheckOut) OR
                                   (CheckInDate >= @CheckIn AND CheckOutDate <= @CheckOut)
                               )";

                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@RoomId", roomId),
                    new MySqlParameter("@CheckIn", checkIn),
                    new MySqlParameter("@CheckOut", checkOut)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) == 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking room availability: {ex.Message}");
                return false;
            }
        }

        public bool UpdateRoomAvailability(int roomId, bool isAvailable)
        {
            try
            {
                string query = "UPDATE Rooms SET IsAvailable = @IsAvailable WHERE RoomId = @RoomId";
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@IsAvailable", isAvailable),
                    new MySqlParameter("@RoomId", roomId)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating room availability: {ex.Message}");
                return false;
            }
        }

        public bool UpdateRoom(Room room)
        {
            try
            {
                string query = @"UPDATE Rooms SET
                                RoomNumber = @RoomNumber,
                                RoomType = @RoomType,
                                Price = @Price,
                                MaxOccupancy = @MaxOccupancy,
                                Description = @Description,
                                IsAvailable = @IsAvailable
                                WHERE RoomId = @RoomId";

                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@RoomNumber", room.RoomNumber),
                    new MySqlParameter("@RoomType", room.RoomType),
                    new MySqlParameter("@Price", room.Price),
                    new MySqlParameter("@MaxOccupancy", room.MaxOccupancy),
                    new MySqlParameter("@Description", room.Description ?? ""),
                    new MySqlParameter("@IsAvailable", room.IsAvailable),
                    new MySqlParameter("@RoomId", room.RoomId)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating room: {ex.Message}");
                return false;
            }
        }
    }
}
