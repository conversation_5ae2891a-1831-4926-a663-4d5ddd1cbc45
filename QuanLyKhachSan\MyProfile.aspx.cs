using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using QuanLyKhachSan.BLL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan
{
    public partial class MyProfile : System.Web.UI.Page
    {
        private UserManager userManager;
        private BookingManager bookingManager;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in
            if (Session["UserId"] == null)
            {
                Response.Redirect("~/Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }

            userManager = new UserManager();
            bookingManager = new BookingManager();

            if (!IsPostBack)
            {
                LoadUserProfile();
                LoadUserBookings();
            }
        }

        private void LoadUserProfile()
        {
            try
            {
                int userId = Convert.ToInt32(Session["UserId"]);
                var user = userManager.GetUserById(userId);

                if (user != null)
                {
                    lblUserName.Text = user.Name;
                    lblUserEmail.Text = user.Email;
                    lblMemberSince.Text = user.CreatedDate.ToString("dd/MM/yyyy");

                    // Load user statistics
                    var userBookings = bookingManager.GetUserBookings(userId);
                    lblTotalBookings.Text = userBookings.Count.ToString();

                    var totalSpent = userBookings
                        .Where(b => b.Status == "Confirmed" || b.Status == "Completed")
                        .Sum(b => b.TotalAmount);
                    lblTotalSpent.Text = totalSpent.ToString("N0");
                }
            }
            catch (Exception ex)
            {
                ShowMessage("Có lỗi xảy ra khi tải thông tin người dùng.", "alert-danger");
                System.Diagnostics.Debug.WriteLine($"Error loading user profile: {ex.Message}");
            }
        }

        private void LoadUserBookings()
        {
            try
            {
                int userId = Convert.ToInt32(Session["UserId"]);
                var bookings = bookingManager.GetUserBookingsWithDetails(userId);

                // Filter by status if selected
                string statusFilter = ddlStatusFilter.SelectedValue;
                if (!string.IsNullOrEmpty(statusFilter))
                {
                    bookings = bookings.Where(b => b.Status == statusFilter).ToList();
                }

                // Sort by booking date descending
                bookings = bookings.OrderByDescending(b => b.BookingDate).ToList();

                if (bookings.Any())
                {
                    rptBookings.DataSource = bookings;
                    rptBookings.DataBind();
                    pnlNoBookings.Visible = false;
                }
                else
                {
                    rptBookings.DataSource = null;
                    rptBookings.DataBind();
                    pnlNoBookings.Visible = true;
                }
            }
            catch (Exception ex)
            {
                ShowMessage("Có lỗi xảy ra khi tải lịch sử đặt phòng.", "alert-danger");
                System.Diagnostics.Debug.WriteLine($"Error loading user bookings: {ex.Message}");
            }
        }

        protected void ddlStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadUserBookings();
        }

        protected void rptBookings_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            int bookingId = Convert.ToInt32(e.CommandArgument);

            if (e.CommandName == "ViewDetails")
            {
                Response.Redirect($"BookingDetails.aspx?id={bookingId}");
            }
            else if (e.CommandName == "CancelBooking")
            {
                try
                {
                    int userId = Convert.ToInt32(Session["UserId"]);
                    bool success = bookingManager.CancelBooking(bookingId, userId);

                    if (success)
                    {
                        ShowMessage("Đặt phòng đã được hủy thành công.", "alert-success");
                        LoadUserBookings(); // Refresh the list
                    }
                    else
                    {
                        ShowMessage("Không thể hủy đặt phòng. Vui lòng kiểm tra lại.", "alert-danger");
                    }
                }
                catch (Exception ex)
                {
                    ShowMessage("Có lỗi xảy ra khi hủy đặt phòng.", "alert-danger");
                    System.Diagnostics.Debug.WriteLine($"Error cancelling booking: {ex.Message}");
                }
            }
        }

        protected void btnEditProfile_Click(object sender, EventArgs e)
        {
            Response.Redirect("EditProfile.aspx");
        }

        protected string GetStatusBadgeClass(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "bg-success";
                case "pending":
                    return "bg-warning text-dark";
                case "cancelled":
                    return "bg-danger";
                case "completed":
                    return "bg-info";
                default:
                    return "bg-secondary";
            }
        }

        protected string GetStatusText(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "Đã xác nhận";
                case "pending":
                    return "Chờ xác nhận";
                case "cancelled":
                    return "Đã hủy";
                case "completed":
                    return "Hoàn thành";
                default:
                    return status;
            }
        }

        protected string GetStatusColor(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "#10B981"; // mint
                case "pending":
                    return "#F59E0B"; // yellow
                case "cancelled":
                    return "#EF4444"; // red
                case "completed":
                    return "#3B82F6"; // blue
                default:
                    return "#6B7280"; // gray
            }
        }

        protected bool CanCancelBooking(string status, DateTime checkInDate)
        {
            // Can only cancel if status is Confirmed or Pending and check-in is at least 24 hours away
            return (status == "Confirmed" || status == "Pending") &&
                   checkInDate.Date > DateTime.Today.AddDays(1);
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }
    }
}
