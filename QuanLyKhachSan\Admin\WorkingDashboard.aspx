<%@ Page Language="C#" AutoEventWireup="true" %>
    <%@ Import Namespace="QuanLyKhachSan.BLL" %>
        <%@ Import Namespace="QuanLyKhachSan.Models" %>

            <!DOCTYPE html>
            <html>

            <head>
                <title>Admin Dashboard</title>
                <link href="../Content/bootstrap.min.css" rel="stylesheet" />
                <link href="../Content/custom-light-theme.css" rel="stylesheet" />
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
                    rel="stylesheet" />
            </head>

            <body>
                <script runat="server">
        private RoomManager roomManager;
        private BookingManager bookingManager;
        private UserManager userManager;

        protected void Page_Load(object sender, EventArgs e)
                    {
                        // Check if user is logged in and is admin
                        if (Session["UserId"] == null) {
                            Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                            return;
                        }

                        if (Session["UserRole"]?.ToString() != "Admin") {
                            Response.Redirect("../Default.aspx");
                            return;
                        }

                        // Initialize managers
                        roomManager = new RoomManager();
                        bookingManager = new BookingManager();
                        userManager = new UserManager();
                    }

        protected int GetTotalRooms()
                    {
                        try {
                            return roomManager.GetAllRooms().Count;
                        }
                        catch {
                            return 0;
                        }
                    }

        protected int GetAvailableRooms()
                    {
                        try {
                            return roomManager.GetAllRooms().Where(r => r.IsAvailable).Count();
                        }
                        catch {
                            return 0;
                        }
                    }

        protected int GetTotalBookings()
                    {
                        try {
                            return bookingManager.GetAllBookings().Count;
                        }
                        catch {
                            return 0;
                        }
                    }

        protected int GetTodayBookings()
                    {
                        try {
                            var today = DateTime.Today;
                            return bookingManager.GetAllBookings().Where(b => b.BookingDate.Date == today).Count();
                        }
                        catch {
                            return 0;
                        }
                    }

        protected int GetTotalUsers()
                    {
                        try {
                            return userManager.GetAllUsers().Where(u => u.Role == "Customer").Count();
                        }
                        catch {
                            return 0;
                        }
                    }

        protected decimal GetTotalRevenue()
                    {
                        try {
                            return bookingManager.GetAllBookings()
                                .Where(b => b.Status == "Confirmed" || b.Status == "Completed")
                                .Sum(b => b.TotalAmount);
                        }
                        catch {
                            return 0;
                        }
                    }

        protected decimal GetTodayRevenue()
                    {
                        try {
                            var today = DateTime.Today;
                            return bookingManager.GetAllBookings()
                                .Where(b => b.BookingDate.Date == today && (b.Status == "Confirmed" || b.Status == "Completed"))
                                .Sum(b => b.TotalAmount);
                        }
                        catch {
                            return 0;
                        }
                    }
                </script>

                <div class="container-fluid mt-4">
                    <!-- Navigation -->
                    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
                        <div class="container-fluid">
                            <a class="navbar-brand" href="../Default.aspx">
                                <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                            </a>
                            <div class="navbar-nav ms-auto">
                                <a class="nav-link" href="../Default.aspx">
                                    <i class="fas fa-home me-1"></i>Trang chủ
                                </a>
                                <a class="nav-link" href="../WorkingTest.aspx">
                                    <i class="fas fa-vial me-1"></i>Test
                                </a>
                            </div>
                        </div>
                    </nav>

                    <!-- Page Header -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                                        <i class="fas fa-tachometer-alt me-3" style="color: var(--accent-blue);"></i>
                                        Admin Dashboard
                                    </h1>
                                    <p class="lead" style="color: var(--text-secondary);">
                                        Tổng quan hệ thống quản lý khách sạn
                                    </p>
                                </div>
                                <div>
                                    <span class="badge bg-success fs-6 px-3 py-2">
                                        <i class="fas fa-user-shield me-2"></i>
                                        <%= Session["UserName"]?.ToString() ?? "Admin" %>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Success Message -->
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Dashboard hoạt động!</strong> Dữ liệu được load từ database thực tế.
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-5">
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stats-card">
                                <div class="stats-number" style="color: var(--accent-blue);">
                                    <i class="fas fa-bed me-2"></i>
                                    <%= GetTotalRooms() %>
                                </div>
                                <p style="color: var(--text-secondary); font-weight: 500;">Tổng số phòng</p>
                                <small style="color: var(--text-muted);">
                                    Phòng trống: <%= GetAvailableRooms() %>
                                </small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stats-card">
                                <div class="stats-number" style="color: var(--accent-mint);">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    <%= GetTotalBookings() %>
                                </div>
                                <p style="color: var(--text-secondary); font-weight: 500;">Tổng đặt phòng</p>
                                <small style="color: var(--text-muted);">
                                    Hôm nay: <%= GetTodayBookings() %>
                                </small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stats-card">
                                <div class="stats-number" style="color: var(--accent-orange);">
                                    <i class="fas fa-users me-2"></i>
                                    <%= GetTotalUsers() %>
                                </div>
                                <p style="color: var(--text-secondary); font-weight: 500;">Tổng khách hàng</p>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stats-card">
                                <div class="stats-number" style="color: var(--accent-yellow);">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    <%= GetTotalRevenue().ToString("N0") %>
                                </div>
                                <p style="color: var(--text-secondary); font-weight: 500;">Doanh thu (VNĐ)</p>
                                <small style="color: var(--text-muted);">
                                    Hôm nay: <%= GetTodayRevenue().ToString("N0") %>
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mb-5">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-bolt me-2" style="color: var(--accent-blue);"></i>
                                        Quản lý hệ thống
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-lg-3 col-md-6">
                                            <a href="ManageRoomsAdvanced.aspx" class="btn btn-primary w-100 py-3">
                                                <i class="fas fa-bed me-2"></i>Quản lý phòng
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="ManageBookingsAdvanced.aspx" class="btn btn-success w-100 py-3">
                                                <i class="fas fa-calendar-check me-2"></i>Quản lý đặt phòng
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="ManageUsers.aspx" class="btn btn-info w-100 py-3">
                                                <i class="fas fa-users me-2"></i>Quản lý khách hàng
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="../Default.aspx" class="btn btn-secondary w-100 py-3">
                                                <i class="fas fa-home me-2"></i>Về trang chủ
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2" style="color: var(--accent-orange);"></i>
                                        Thông tin hệ thống
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Server Time:</span>
                                            <strong>
                                                <%= DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") %>
                                            </strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Admin User:</span>
                                            <strong>
                                                <%= Session["UserName"]?.ToString() ?? "Admin" %>
                                            </strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Session ID:</span>
                                            <strong>
                                                <%= Session.SessionID %>
                                            </strong>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-pie me-2" style="color: var(--accent-purple);"></i>
                                        Trạng thái
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        Hệ thống đang hoạt động bình thường
                                    </div>
                                    <div class="alert alert-info">
                                        <i class="fas fa-database me-2"></i>
                                        Database kết nối thành công
                                    </div>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Đây là phiên bản Working Dashboard (không có lỗi Parser)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script src="../Scripts/bootstrap.bundle.min.js"></script>
            </body>

            </html>