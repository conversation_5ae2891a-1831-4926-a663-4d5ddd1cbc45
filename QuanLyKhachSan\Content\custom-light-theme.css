/* ===== LIGHT THEME HOTEL BOOKING SYSTEM ===== */

/* Root Variables */
:root {
    --primary-bg: #F9FAFB;
    --secondary-bg: #FFFFFF;
    --card-bg: #FFFFFF;
    --card-hover-bg: #F3F4F6;
    --text-primary: #111827;
    --text-secondary: #6B7280;
    --text-muted: #9CA3AF;
    --accent-blue: #3B82F6;
    --accent-mint: #10B981;
    --accent-yellow: #F59E0B;
    --accent-orange: #F97316;
    --accent-purple: #8B5CF6;
    --border-color: #E5E7EB;
    --border-light: #D1D5DB;
    --success: #10B981;
    --warning: #F59E0B;
    --danger: #EF4444;
    --info: #3B82F6;
}

/* Global Styles */
body {
    background-color: var(--primary-bg) !important;
    color: var(--text-primary) !important;
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    min-height: 100vh;
}

/* Navbar Styling */
.navbar-dark {
    background: var(--secondary-bg) !important;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--accent-blue) !important;
    transition: color 0.3s ease;
}

.navbar-brand:hover {
    color: var(--accent-mint) !important;
}

.navbar-nav .nav-link {
    color: var(--text-primary) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 8px 16px;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-blue) !important;
    transform: translateY(-1px);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: var(--accent-blue);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
    left: 0;
}

/* Cards */
.card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    background-color: var(--card-hover-bg) !important;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
    border-color: var(--accent-blue) !important;
}

.card-header {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-mint) 100%) !important;
    border-bottom: 1px solid var(--border-color) !important;
    color: white !important;
    font-weight: 600;
}

.card-body {
    color: var(--text-primary) !important;
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--primary-bg) !important;
    border-top: 1px solid var(--border-color) !important;
}

/* Buttons */
.btn-primary {
    background: var(--accent-blue) !important;
    border: none !important;
    color: white !important;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    background: #2563EB !important;
}

.btn-secondary {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-light) !important;
    color: var(--text-primary) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--card-hover-bg) !important;
    border-color: var(--accent-blue) !important;
    color: var(--accent-blue) !important;
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success) !important;
    border: none !important;
    color: white !important;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    background: #059669 !important;
    transform: translateY(-2px);
}

.btn-info {
    background: var(--info) !important;
    border: none !important;
    color: white !important;
    border-radius: 8px;
}

.btn-outline-primary {
    border: 2px solid var(--accent-blue) !important;
    color: var(--accent-blue) !important;
    background: transparent !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--accent-blue) !important;
    color: white !important;
    transform: translateY(-2px);
}

/* Forms */
.form-control {
    background-color: var(--secondary-bg) !important;
    border: 1px solid var(--border-light) !important;
    color: var(--text-primary) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    padding: 12px 16px;
}

.form-control:focus {
    background-color: var(--secondary-bg) !important;
    border-color: var(--accent-blue) !important;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.15) !important;
    color: var(--text-primary) !important;
}

.form-control::placeholder {
    color: var(--text-muted) !important;
}

.form-label,
label {
    color: var(--text-primary) !important;
    font-weight: 500;
    margin-bottom: 8px;
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: 1px solid;
    font-weight: 500;
    padding: 16px;
}

.alert-success {
    background-color: #ECFDF5 !important;
    color: #065F46 !important;
    border-color: #A7F3D0 !important;
}

.alert-danger {
    background-color: #FEF2F2 !important;
    color: #991B1B !important;
    border-color: #FECACA !important;
}

.alert-warning {
    background-color: #FFFBEB !important;
    color: #92400E !important;
    border-color: #FDE68A !important;
}

.alert-info {
    background-color: #EFF6FF !important;
    color: #1E40AF !important;
    border-color: #BFDBFE !important;
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 6px 12px;
}

.badge-success {
    background: var(--success) !important;
    color: white !important;
}

.badge-danger {
    background: var(--danger) !important;
    color: white !important;
}

/* Tables */
.table {
    color: var(--text-primary) !important;
}

.table-borderless td {
    border: none !important;
    padding: 8px 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%233B82F6" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.5;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.display-4 {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-mint) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Stats Cards */
.stats-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-mint), var(--accent-purple));
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

/* Breadcrumb */
.breadcrumb {
    background: transparent !important;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--accent-blue) !important;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--accent-mint) !important;
}

.breadcrumb-item.active {
    color: var(--text-secondary) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--primary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-blue);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-mint);
}

/* Dropdown */
.dropdown-menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 8px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
    color: var(--text-primary) !important;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--card-hover-bg) !important;
    color: var(--accent-blue) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-mint) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Custom Input Focus */
.form-control:focus {
    border-color: var(--accent-blue) !important;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.15) !important;
    background-color: var(--secondary-bg) !important;
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

/* Glow Effect */
.glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    transition: box-shadow 0.3s ease;
}

.glow:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}

/* Room Card Enhancements */
.room-card {
    position: relative;
    overflow: hidden;
    border-radius: 15px !important;
}

.room-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-mint), var(--accent-purple));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.room-card:hover::before {
    opacity: 1;
}

/* Enhanced Button Styles */
.btn-gradient {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-mint) 100%);
    border: none;
    color: white;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-mint) 0%, var(--accent-blue) 100%);
    transition: left 0.3s ease;
    z-index: 0;
}

.btn-gradient:hover::before {
    left: 0;
}

.btn-gradient span {
    position: relative;
    z-index: 1;
}

/* Footer Enhancements */
.footer {
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--primary-bg) 100%);
    border-top: 1px solid var(--border-color);
    padding: 40px 0 20px;
    margin-top: 60px;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }

    .display-4 {
        font-size: 2rem;
    }

    .stats-card {
        margin-bottom: 20px;
    }

    .navbar-nav .nav-link {
        padding: 10px 15px;
    }

    .card-body {
        padding: 20px;
    }

    .btn-lg {
        padding: 12px 30px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 40px 0;
    }

    .display-4 {
        font-size: 1.8rem;
    }

    .stats-number {
        font-size: 2rem;
    }

    .card {
        margin-bottom: 20px;
    }
}