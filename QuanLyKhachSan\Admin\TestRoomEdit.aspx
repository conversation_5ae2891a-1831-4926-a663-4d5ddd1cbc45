<%@ Page Language="C#" AutoEventWireup="true" %>
<%@ Import Namespace="QuanLyKhachSan.BLL" %>
<%@ Import Namespace="QuanLyKhachSan.Models" %>

<!DOCTYPE html>
<html>
<head>
    <title>Test Room Edit Functions</title>
    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <script runat="server">
        private RoomManager roomManager;
        private string testResult = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in and is admin
            if (Session["UserId"] == null)
            {
                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }
            
            if (Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("../Default.aspx");
                return;
            }

            roomManager = new RoomManager();
            
            if (Request.QueryString["test"] != null)
            {
                RunTests();
            }
        }

        private void RunTests()
        {
            testResult += "<h4>🧪 Test Results:</h4>";
            
            try
            {
                // Test 1: Get all rooms
                var rooms = roomManager.GetAllRooms();
                testResult += $"<div class='alert alert-info'>✅ GetAllRooms(): Found {rooms.Count} rooms</div>";
                
                if (rooms.Any())
                {
                    var firstRoom = rooms.First();
                    testResult += $"<div class='alert alert-secondary'>📋 First room: {firstRoom.RoomNumber} - {firstRoom.RoomType} - {firstRoom.Price:N0} VNĐ</div>";
                    
                    // Test 2: Get room by ID
                    var roomById = roomManager.GetRoomById(firstRoom.RoomId);
                    if (roomById != null)
                    {
                        testResult += $"<div class='alert alert-success'>✅ GetRoomById({firstRoom.RoomId}): Success</div>";
                        
                        // Test 3: Update room (change description)
                        string originalDesc = roomById.Description;
                        roomById.Description = "Test description updated at " + DateTime.Now.ToString("HH:mm:ss");
                        
                        bool updateSuccess = roomManager.UpdateRoom(roomById);
                        if (updateSuccess)
                        {
                            testResult += "<div class='alert alert-success'>✅ UpdateRoom(): Success - Description updated</div>";
                            
                            // Verify update
                            var updatedRoom = roomManager.GetRoomById(firstRoom.RoomId);
                            if (updatedRoom.Description == roomById.Description)
                            {
                                testResult += "<div class='alert alert-success'>✅ Update verification: Success</div>";
                                
                                // Restore original description
                                updatedRoom.Description = originalDesc;
                                roomManager.UpdateRoom(updatedRoom);
                                testResult += "<div class='alert alert-info'>🔄 Original description restored</div>";
                            }
                            else
                            {
                                testResult += "<div class='alert alert-warning'>⚠️ Update verification: Failed</div>";
                            }
                        }
                        else
                        {
                            testResult += "<div class='alert alert-danger'>❌ UpdateRoom(): Failed</div>";
                        }
                        
                        // Test 4: Toggle availability
                        bool originalAvailability = roomById.IsAvailable;
                        roomById.IsAvailable = !originalAvailability;
                        
                        bool toggleSuccess = roomManager.UpdateRoom(roomById);
                        if (toggleSuccess)
                        {
                            testResult += $"<div class='alert alert-success'>✅ Toggle availability: {originalAvailability} → {!originalAvailability}</div>";
                            
                            // Restore original availability
                            roomById.IsAvailable = originalAvailability;
                            roomManager.UpdateRoom(roomById);
                            testResult += "<div class='alert alert-info'>🔄 Original availability restored</div>";
                        }
                        else
                        {
                            testResult += "<div class='alert alert-danger'>❌ Toggle availability: Failed</div>";
                        }
                    }
                    else
                    {
                        testResult += $"<div class='alert alert-danger'>❌ GetRoomById({firstRoom.RoomId}): Failed</div>";
                    }
                }
                else
                {
                    testResult += "<div class='alert alert-warning'>⚠️ No rooms found in database</div>";
                }
            }
            catch (Exception ex)
            {
                testResult += $"<div class='alert alert-danger'>❌ Test failed with error: {ex.Message}</div>";
            }
        }
    </script>

    <div class="container mt-4">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="../Default.aspx">
                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="WorkingDashboard.aspx">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                    <a class="nav-link" href="../WorkingTest.aspx">
                        <i class="fas fa-vial me-1"></i>Main Test
                    </a>
                </div>
            </div>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                    <i class="fas fa-vial me-3" style="color: var(--accent-blue);"></i>
                    Test Room Edit Functions
                </h1>
                <p class="lead" style="color: var(--text-secondary);">
                    Kiểm tra chức năng chỉnh sửa phòng và thay đổi trạng thái
                </p>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-play me-2" style="color: var(--accent-mint);"></i>
                            Test Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-3 flex-wrap">
                            <a href="?test=run" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>Run Tests
                            </a>
                            <a href="ManageRoomsAdvanced.aspx" class="btn btn-success">
                                <i class="fas fa-bed me-2"></i>Go to Room Management
                            </a>
                            <a href="WorkingDashboard.aspx" class="btn btn-secondary">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <% if (!string.IsNullOrEmpty(testResult)) { %>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-check me-2" style="color: var(--accent-orange);"></i>
                                Test Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <%= testResult %>
                        </div>
                    </div>
                </div>
            </div>
        <% } else { %>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-flask fa-4x mb-4" style="color: var(--text-muted);"></i>
                            <h4 style="color: var(--text-secondary);">Ready to Test</h4>
                            <p style="color: var(--text-muted);">Click "Run Tests" to test room edit functions</p>
                        </div>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Function Overview -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2" style="color: var(--accent-blue);"></i>
                            Functions Being Tested
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 style="color: var(--text-primary);">RoomManager Methods:</h6>
                                <ul style="color: var(--text-secondary);">
                                    <li>GetAllRooms()</li>
                                    <li>GetRoomById(int id)</li>
                                    <li>UpdateRoom(Room room)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 style="color: var(--text-primary);">Test Scenarios:</h6>
                                <ul style="color: var(--text-secondary);">
                                    <li>Load room data</li>
                                    <li>Update room description</li>
                                    <li>Toggle room availability</li>
                                    <li>Verify changes</li>
                                    <li>Restore original values</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../Scripts/bootstrap.bundle.min.js"></script>
</body>
</html>
