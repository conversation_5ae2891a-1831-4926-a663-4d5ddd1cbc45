﻿using System;
using System.Web.UI;

namespace QuanLyKhachSan
{
    public partial class SiteMaster : MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            UpdateUserInterface();
        }

        private void UpdateUserInterface()
        {
            if (Session["UserId"] != null)
            {
                // User is logged in
                pnlUserLoggedIn.Visible = true;
                pnlUserNotLoggedIn.Visible = false;
                lblUserName.Text = Session["UserName"]?.ToString() ?? "Người dùng";
            }
            else
            {
                // User is not logged in
                pnlUserLoggedIn.Visible = false;
                pnlUserNotLoggedIn.Visible = true;
            }
        }

        protected void lnkLogout_Click(object sender, EventArgs e)
        {
            // Clear all session data
            Session.Clear();
            Session.Abandon();

            // Redirect to home page
            Response.Redirect("Default.aspx");
        }
    }
}