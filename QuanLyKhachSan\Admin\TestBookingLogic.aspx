<%@ Page Language="C#" AutoEventWireup="true" %>
<%@ Import Namespace="QuanLyKhachSan.BLL" %>
<%@ Import Namespace="QuanLyKhachSan.Models" %>

<!DOCTYPE html>
<html>
<head>
    <title>Test Booking Logic</title>
    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <script runat="server">
        private RoomManager roomManager;
        private BookingManager bookingManager;
        private string testResult = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in and is admin
            if (Session["UserId"] == null)
            {
                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }
            
            if (Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("../Default.aspx");
                return;
            }

            roomManager = new RoomManager();
            bookingManager = new BookingManager();
            
            if (Request.QueryString["test"] != null)
            {
                RunBookingLogicTests();
            }
        }

        private void RunBookingLogicTests()
        {
            testResult += "<h4>🧪 Booking Logic Test Results:</h4>";
            
            try
            {
                // Test dates
                DateTime checkIn = DateTime.Today.AddDays(1);
                DateTime checkOut = DateTime.Today.AddDays(3);
                
                testResult += $"<div class='alert alert-info'>📅 Test period: {checkIn:dd/MM/yyyy} - {checkOut:dd/MM/yyyy}</div>";
                
                // Test 1: Get all rooms
                var allRooms = roomManager.GetAllRooms();
                testResult += $"<div class='alert alert-info'>✅ Total rooms in system: {allRooms.Count}</div>";
                
                // Test 2: Get available rooms for period
                var availableRooms = roomManager.GetAvailableRoomsForPeriod(checkIn, checkOut);
                testResult += $"<div class='alert alert-success'>✅ Available rooms for period: {availableRooms.Count}</div>";
                
                if (availableRooms.Any())
                {
                    var testRoom = availableRooms.First();
                    testResult += $"<div class='alert alert-secondary'>📋 Test room: {testRoom.RoomNumber} - {testRoom.RoomType}</div>";
                    
                    // Test 3: Check room availability
                    bool isAvailable = roomManager.IsRoomAvailable(testRoom.RoomId, checkIn, checkOut);
                    testResult += $"<div class='alert alert-{(isAvailable ? "success" : "danger")}'>✅ IsRoomAvailable check: {isAvailable}</div>";
                    
                    // Test 4: Get room bookings for period
                    var roomBookings = roomManager.GetRoomBookingsForPeriod(testRoom.RoomId, checkIn, checkOut);
                    testResult += $"<div class='alert alert-info'>📋 Existing bookings for room in period: {roomBookings.Count}</div>";
                    
                    foreach (var booking in roomBookings)
                    {
                        testResult += $"<div class='alert alert-warning'>⚠️ Conflict: Booking #{booking.BookingId} ({booking.CheckInDate:dd/MM/yyyy} - {booking.CheckOutDate:dd/MM/yyyy}) Status: {booking.Status}</div>";
                    }
                }
                
                // Test 5: Get all bookings
                var allBookings = bookingManager.GetAllBookings();
                testResult += $"<div class='alert alert-info'>📊 Total bookings in system: {allBookings.Count}</div>";
                
                var confirmedBookings = allBookings.Where(b => b.Status == "Confirmed").Count();
                var pendingBookings = allBookings.Where(b => b.Status == "Pending").Count();
                var cancelledBookings = allBookings.Where(b => b.Status == "Cancelled").Count();
                
                testResult += $"<div class='alert alert-success'>✅ Confirmed: {confirmedBookings}, Pending: {pendingBookings}, Cancelled: {cancelledBookings}</div>";
                
                // Test 6: Test different date ranges
                testResult += "<h5>🔍 Testing different date ranges:</h5>";
                
                var testDates = new[]
                {
                    new { CheckIn = DateTime.Today.AddDays(1), CheckOut = DateTime.Today.AddDays(2), Name = "Tomorrow (1 night)" },
                    new { CheckIn = DateTime.Today.AddDays(7), CheckOut = DateTime.Today.AddDays(10), Name = "Next week (3 nights)" },
                    new { CheckIn = DateTime.Today.AddDays(30), CheckOut = DateTime.Today.AddDays(33), Name = "Next month (3 nights)" }
                };
                
                foreach (var testDate in testDates)
                {
                    var available = roomManager.GetAvailableRoomsForPeriod(testDate.CheckIn, testDate.CheckOut);
                    testResult += $"<div class='alert alert-secondary'>📅 {testDate.Name}: {available.Count} rooms available</div>";
                }
                
            }
            catch (Exception ex)
            {
                testResult += $"<div class='alert alert-danger'>❌ Test failed with error: {ex.Message}</div>";
                testResult += $"<div class='alert alert-danger'>Stack trace: {ex.StackTrace}</div>";
            }
        }
    </script>

    <div class="container mt-4">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="../Default.aspx">
                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="WorkingDashboard.aspx">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                    <a class="nav-link" href="../WorkingTest.aspx">
                        <i class="fas fa-vial me-1"></i>Main Test
                    </a>
                </div>
            </div>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                    <i class="fas fa-calendar-check me-3" style="color: var(--accent-blue);"></i>
                    Test Booking Logic
                </h1>
                <p class="lead" style="color: var(--text-secondary);">
                    Kiểm tra logic đặt phòng và kiểm tra phòng trống theo khoảng thời gian
                </p>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-play me-2" style="color: var(--accent-mint);"></i>
                            Test Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-3 flex-wrap">
                            <a href="?test=run" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>Run Booking Logic Tests
                            </a>
                            <a href="ManageBookingsAdvanced.aspx" class="btn btn-success">
                                <i class="fas fa-calendar-check me-2"></i>Go to Booking Management
                            </a>
                            <a href="ManageRoomsAdvanced.aspx" class="btn btn-info">
                                <i class="fas fa-bed me-2"></i>Go to Room Management
                            </a>
                            <a href="WorkingDashboard.aspx" class="btn btn-secondary">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <% if (!string.IsNullOrEmpty(testResult)) { %>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-check me-2" style="color: var(--accent-orange);"></i>
                                Test Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <%= testResult %>
                        </div>
                    </div>
                </div>
            </div>
        <% } else { %>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-calendar-alt fa-4x mb-4" style="color: var(--text-muted);"></i>
                            <h4 style="color: var(--text-secondary);">Ready to Test Booking Logic</h4>
                            <p style="color: var(--text-muted);">Click "Run Booking Logic Tests" to test room availability logic</p>
                        </div>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Logic Overview -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2" style="color: var(--accent-blue);"></i>
                            Booking Logic Being Tested
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 style="color: var(--text-primary);">Room Availability Logic:</h6>
                                <ul style="color: var(--text-secondary);">
                                    <li>GetAvailableRoomsForPeriod() - Tìm phòng trống trong khoảng thời gian</li>
                                    <li>IsRoomAvailable() - Kiểm tra phòng cụ thể có trống không</li>
                                    <li>GetRoomBookingsForPeriod() - Lấy đặt phòng trùng thời gian</li>
                                    <li>Logic: Phòng trống nếu KHÔNG có booking Confirmed/Pending trùng thời gian</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 style="color: var(--text-primary);">Test Scenarios:</h6>
                                <ul style="color: var(--text-secondary);">
                                    <li>Kiểm tra phòng trống cho ngày mai</li>
                                    <li>Kiểm tra phòng trống cho tuần tới</li>
                                    <li>Kiểm tra phòng trống cho tháng tới</li>
                                    <li>Tìm conflicts với booking hiện tại</li>
                                    <li>Thống kê booking theo status</li>
                                </ul>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>Logic Explanation:</h6>
                            <p class="mb-0">
                                <strong>Phòng được coi là "trống" cho khoảng thời gian [CheckIn, CheckOut] nếu:</strong><br>
                                1. Phòng có IsAvailable = true<br>
                                2. KHÔNG có booking nào với Status = 'Confirmed' hoặc 'Pending' mà thời gian trùng lặp<br>
                                3. Trùng lặp được tính: NOT (CheckOut <= booking.CheckIn OR CheckIn >= booking.CheckOut)
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../Scripts/bootstrap.bundle.min.js"></script>
</body>
</html>
