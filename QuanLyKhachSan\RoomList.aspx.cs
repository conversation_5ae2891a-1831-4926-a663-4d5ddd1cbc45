using System;
using System.Collections.Generic;
using System.Web.UI;
using System.Web.UI.WebControls;
using QuanLyKhachSan.BLL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan
{
    public partial class RoomList : Page
    {
        private RoomManager roomManager;

        protected void Page_Load(object sender, EventArgs e)
        {
            roomManager = new RoomManager();

            if (!IsPostBack)
            {
                LoadRoomTypes();
                SetDefaultDates();
                LoadRooms();
            }
        }

        private void LoadRoomTypes()
        {
            try
            {
                var roomTypes = roomManager.GetAvailableRoomTypes();
                foreach (var roomType in roomTypes)
                {
                    ddlRoomType.Items.Add(new ListItem(roomType, roomType));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading room types: {ex.Message}");
            }
        }

        private void SetDefaultDates()
        {
            txtCheckIn.Text = DateTime.Today.ToString("yyyy-MM-dd");
            txtCheckOut.Text = DateTime.Today.AddDays(1).ToString("yyyy-MM-dd");
        }

        private void LoadRooms()
        {
            try
            {
                List<Room> rooms;

                // Check if search criteria are provided
                if (!string.IsNullOrEmpty(txtCheckIn.Text) && !string.IsNullOrEmpty(txtCheckOut.Text))
                {
                    DateTime checkIn = DateTime.Parse(txtCheckIn.Text);
                    DateTime checkOut = DateTime.Parse(txtCheckOut.Text);

                    if (checkIn >= checkOut)
                    {
                        ShowMessage("Ngày trả phòng phải sau ngày nhận phòng.", "alert-warning");
                        rooms = roomManager.GetAvailableRooms();
                    }
                    else
                    {
                        rooms = roomManager.GetAvailableRoomsForDates(checkIn, checkOut);
                    }
                }
                else
                {
                    rooms = roomManager.GetAvailableRooms();
                }

                // Apply room type filter
                if (!string.IsNullOrEmpty(ddlRoomType.SelectedValue))
                {
                    rooms = rooms.FindAll(r => r.RoomType == ddlRoomType.SelectedValue);
                }

                if (rooms.Count > 0)
                {
                    rptRooms.DataSource = rooms;
                    rptRooms.DataBind();
                    pnlNoRooms.Visible = false;
                }
                else
                {
                    rptRooms.DataSource = null;
                    rptRooms.DataBind();
                    pnlNoRooms.Visible = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading rooms: {ex.Message}");
                ShowMessage("Có lỗi xảy ra khi tải danh sách phòng.", "alert-danger");
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            LoadRooms();
        }

        protected void btnReset_Click(object sender, EventArgs e)
        {
            ddlRoomType.SelectedIndex = 0;
            SetDefaultDates();
            LoadRooms();
            pnlMessage.Visible = false;
        }

        protected void rptRooms_ItemCommand(object source, RepeaterCommandEventArgs e)
        {
            int roomId = Convert.ToInt32(e.CommandArgument);

            switch (e.CommandName)
            {
                case "ViewDetails":
                    Response.Redirect($"RoomDetails.aspx?roomId={roomId}");
                    break;

                case "BookRoom":
                    // Check if user is logged in
                    if (Session["UserId"] == null)
                    {
                        Response.Redirect($"Login.aspx?ReturnUrl=BookRoom.aspx?roomId={roomId}");
                    }
                    else
                    {
                        string checkIn = txtCheckIn.Text;
                        string checkOut = txtCheckOut.Text;
                        Response.Redirect($"BookRoom.aspx?roomId={roomId}&checkIn={checkIn}&checkOut={checkOut}");
                    }
                    break;
            }
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }
    }
}
