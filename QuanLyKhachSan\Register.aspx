<%@ Page Title="Đăng ký" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true"
    CodeBehind="Register.aspx.cs" Inherits="QuanLyKhachSan.Register" %>

    <asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-lg-6 col-md-8">
                    <div class="card fade-in">
                        <div class="card-header text-center">
                            <div class="mb-3">
                                <i class="fas fa-user-plus fa-3x" style="color: var(--accent-blue);"></i>
                            </div>
                            <h3 style="color: var(--text-primary); font-weight: 600;">Đăng ký tài khoản</h3>
                            <p style="color: var(--text-secondary);">Tạo tài khoản để trải nghiệm dịch v<PERSON> tuyệt vời</p>
                        </div>
                        <div class="card-body">
                            <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
                                <asp:Label ID="lblMessage" runat="server"></asp:Label>
                            </asp:Panel>

                            <div class="form-group mb-4">
                                <label for="txtName" class="form-label">
                                    <i class="fas fa-user me-2" style="color: var(--accent-mint);"></i>
                                    Họ và tên:
                                </label>
                                <asp:TextBox ID="txtName" runat="server" CssClass="form-control"
                                    placeholder="Nhập họ và tên đầy đủ"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvName" runat="server" ControlToValidate="txtName"
                                    ErrorMessage="Vui lòng nhập họ và tên" CssClass="text-danger" Display="Dynamic">
                                </asp:RequiredFieldValidator>
                            </div>

                            <div class="form-group mb-4">
                                <label for="txtEmail" class="form-label">
                                    <i class="fas fa-envelope me-2" style="color: var(--accent-blue);"></i>
                                    Email:
                                </label>
                                <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" TextMode="Email"
                                    placeholder="Nhập địa chỉ email của bạn"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvEmail" runat="server" ControlToValidate="txtEmail"
                                    ErrorMessage="Vui lòng nhập email" CssClass="text-danger" Display="Dynamic">
                                </asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revEmail" runat="server"
                                    ControlToValidate="txtEmail" ErrorMessage="Email không hợp lệ"
                                    ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                                    CssClass="text-danger" Display="Dynamic">
                                </asp:RegularExpressionValidator>
                            </div>

                            <div class="form-group mb-3">
                                <label for="txtPassword">Mật khẩu:</label>
                                <asp:TextBox ID="txtPassword" runat="server" CssClass="form-control" TextMode="Password"
                                    placeholder="Nhập mật khẩu"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvPassword" runat="server"
                                    ControlToValidate="txtPassword" ErrorMessage="Vui lòng nhập mật khẩu"
                                    CssClass="text-danger" Display="Dynamic">
                                </asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revPassword" runat="server"
                                    ControlToValidate="txtPassword" ErrorMessage="Mật khẩu phải có ít nhất 6 ký tự"
                                    ValidationExpression=".{6,}" CssClass="text-danger" Display="Dynamic">
                                </asp:RegularExpressionValidator>
                            </div>

                            <div class="form-group mb-3">
                                <label for="txtConfirmPassword">Xác nhận mật khẩu:</label>
                                <asp:TextBox ID="txtConfirmPassword" runat="server" CssClass="form-control"
                                    TextMode="Password" placeholder="Nhập lại mật khẩu"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvConfirmPassword" runat="server"
                                    ControlToValidate="txtConfirmPassword" ErrorMessage="Vui lòng xác nhận mật khẩu"
                                    CssClass="text-danger" Display="Dynamic">
                                </asp:RequiredFieldValidator>
                                <asp:CompareValidator ID="cvPassword" runat="server"
                                    ControlToValidate="txtConfirmPassword" ControlToCompare="txtPassword"
                                    ErrorMessage="Mật khẩu xác nhận không khớp" CssClass="text-danger"
                                    Display="Dynamic">
                                </asp:CompareValidator>
                            </div>

                            <div class="form-group text-center">
                                <asp:Button ID="btnRegister" runat="server" Text="Đăng ký"
                                    CssClass="btn btn-primary btn-lg" OnClick="btnRegister_Click" />
                            </div>

                            <div class="text-center mt-3">
                                <p>Đã có tài khoản? <a href="Login.aspx">Đăng nhập ngay</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </asp:Content>