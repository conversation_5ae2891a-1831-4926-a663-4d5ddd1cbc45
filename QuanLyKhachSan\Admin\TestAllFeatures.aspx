<%@ Page Language="C#" AutoEventWireup="true" %>
<%@ Import Namespace="QuanLyKhachSan.BLL" %>
<%@ Import Namespace="QuanLyKhachSan.Models" %>

<!DOCTYPE html>
<html>
<head>
    <title>Test All Features</title>
    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <script runat="server">
        private string testResult = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in and is admin
            if (Session["UserId"] == null)
            {
                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }
            
            if (Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("../Default.aspx");
                return;
            }
            
            if (Request.QueryString["test"] != null)
            {
                RunAllTests();
            }
        }

        private void RunAllTests()
        {
            testResult += "<h4>🧪 Complete System Test Results:</h4>";
            
            try
            {
                // Test 1: Database Connection
                testResult += "<h5>1. Database Connection Test:</h5>";
                bool dbConnection = TestDatabaseConnection();
                testResult += $"<div class='alert alert-{(dbConnection ? "success" : "danger")}'>Database Connection: {(dbConnection ? "✅ Success" : "❌ Failed")}</div>";
                
                // Test 2: Room Management
                testResult += "<h5>2. Room Management Test:</h5>";
                bool roomTest = TestRoomManagement();
                testResult += $"<div class='alert alert-{(roomTest ? "success" : "danger")}'>Room Management: {(roomTest ? "✅ Success" : "❌ Failed")}</div>";
                
                // Test 3: Booking Management
                testResult += "<h5>3. Booking Management Test:</h5>";
                bool bookingTest = TestBookingManagement();
                testResult += $"<div class='alert alert-{(bookingTest ? "success" : "danger")}'>Booking Management: {(bookingTest ? "✅ Success" : "❌ Failed")}</div>";
                
                // Test 4: User Management
                testResult += "<h5>4. User Management Test:</h5>";
                bool userTest = TestUserManagement();
                testResult += $"<div class='alert alert-{(userTest ? "success" : "danger")}'>User Management: {(userTest ? "✅ Success" : "❌ Failed")}</div>";
                
                // Test 5: Booking Logic
                testResult += "<h5>5. Booking Logic Test:</h5>";
                bool logicTest = TestBookingLogic();
                testResult += $"<div class='alert alert-{(logicTest ? "success" : "danger")}'>Booking Logic: {(logicTest ? "✅ Success" : "❌ Failed")}</div>";
                
                // Overall result
                bool allPassed = dbConnection && roomTest && bookingTest && userTest && logicTest;
                testResult += $"<div class='alert alert-{(allPassed ? "success" : "warning")} mt-4'><h5>Overall Result: {(allPassed ? "🎉 All Tests Passed!" : "⚠️ Some Tests Failed")}</h5></div>";
                
            }
            catch (Exception ex)
            {
                testResult += $"<div class='alert alert-danger'>❌ Test suite failed with error: {ex.Message}</div>";
            }
        }

        private bool TestDatabaseConnection()
        {
            try
            {
                return DatabaseHelper.TestConnection();
            }
            catch (Exception ex)
            {
                testResult += $"<div class='alert alert-danger'>Database error: {ex.Message}</div>";
                return false;
            }
        }

        private bool TestRoomManagement()
        {
            try
            {
                var roomManager = new RoomManager();
                var rooms = roomManager.GetAllRooms();
                testResult += $"<div class='alert alert-info'>Found {rooms.Count} rooms in database</div>";
                
                if (rooms.Any())
                {
                    var firstRoom = rooms.First();
                    var roomById = roomManager.GetRoomById(firstRoom.RoomId);
                    if (roomById != null)
                    {
                        testResult += $"<div class='alert alert-success'>GetRoomById working for room {roomById.RoomNumber}</div>";
                        return true;
                    }
                }
                return rooms.Count > 0;
            }
            catch (Exception ex)
            {
                testResult += $"<div class='alert alert-danger'>Room management error: {ex.Message}</div>";
                return false;
            }
        }

        private bool TestBookingManagement()
        {
            try
            {
                var bookingManager = new BookingManager();
                var bookings = bookingManager.GetAllBookings();
                testResult += $"<div class='alert alert-info'>Found {bookings.Count} bookings in database</div>";
                
                var confirmedCount = bookings.Where(b => b.Status == "Confirmed").Count();
                var pendingCount = bookings.Where(b => b.Status == "Pending").Count();
                testResult += $"<div class='alert alert-info'>Confirmed: {confirmedCount}, Pending: {pendingCount}</div>";
                
                return true;
            }
            catch (Exception ex)
            {
                testResult += $"<div class='alert alert-danger'>Booking management error: {ex.Message}</div>";
                return false;
            }
        }

        private bool TestUserManagement()
        {
            try
            {
                var userManager = new UserManager();
                var users = userManager.GetAllUsers();
                testResult += $"<div class='alert alert-info'>Found {users.Count} users in database</div>";
                
                var adminCount = users.Where(u => u.Role == "Admin").Count();
                var customerCount = users.Where(u => u.Role == "Customer").Count();
                testResult += $"<div class='alert alert-info'>Admins: {adminCount}, Customers: {customerCount}</div>";
                
                return users.Count > 0;
            }
            catch (Exception ex)
            {
                testResult += $"<div class='alert alert-danger'>User management error: {ex.Message}</div>";
                return false;
            }
        }

        private bool TestBookingLogic()
        {
            try
            {
                var roomManager = new RoomManager();
                DateTime checkIn = DateTime.Today.AddDays(1);
                DateTime checkOut = DateTime.Today.AddDays(3);
                
                var availableRooms = roomManager.GetAvailableRoomsForPeriod(checkIn, checkOut);
                testResult += $"<div class='alert alert-info'>Available rooms for {checkIn:dd/MM/yyyy}-{checkOut:dd/MM/yyyy}: {availableRooms.Count}</div>";
                
                if (availableRooms.Any())
                {
                    var testRoom = availableRooms.First();
                    bool isAvailable = roomManager.IsRoomAvailable(testRoom.RoomId, checkIn, checkOut);
                    testResult += $"<div class='alert alert-success'>Room {testRoom.RoomNumber} availability check: {isAvailable}</div>";
                }
                
                return true;
            }
            catch (Exception ex)
            {
                testResult += $"<div class='alert alert-danger'>Booking logic error: {ex.Message}</div>";
                return false;
            }
        }
    </script>

    <div class="container mt-4">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="../Default.aspx">
                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="WorkingDashboard.aspx">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                    <a class="nav-link" href="../WorkingTest.aspx">
                        <i class="fas fa-vial me-1"></i>Main Test
                    </a>
                </div>
            </div>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                    <i class="fas fa-clipboard-check me-3" style="color: var(--accent-blue);"></i>
                    Test All Features
                </h1>
                <p class="lead" style="color: var(--text-secondary);">
                    Kiểm tra tổng thể tất cả chức năng của hệ thống
                </p>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-play me-2" style="color: var(--accent-mint);"></i>
                            Test Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-3 flex-wrap">
                            <a href="?test=run" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>Run All Tests
                            </a>
                            <a href="WorkingDashboard.aspx" class="btn btn-success">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                            <a href="ManageRoomsAdvanced.aspx" class="btn btn-info">
                                <i class="fas fa-bed me-2"></i>Room Management
                            </a>
                            <a href="ManageBookingsAdvanced.aspx" class="btn btn-warning">
                                <i class="fas fa-calendar-check me-2"></i>Booking Management
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <% if (!string.IsNullOrEmpty(testResult)) { %>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-check me-2" style="color: var(--accent-orange);"></i>
                                Complete Test Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <%= testResult %>
                        </div>
                    </div>
                </div>
            </div>
        <% } else { %>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-rocket fa-4x mb-4" style="color: var(--text-muted);"></i>
                            <h4 style="color: var(--text-secondary);">Ready to Test Everything</h4>
                            <p style="color: var(--text-muted);">Click "Run All Tests" to test the complete system</p>
                        </div>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Feature Overview -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check me-2" style="color: var(--accent-blue);"></i>
                            Features Being Tested
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 style="color: var(--text-primary);">Core Features:</h6>
                                <ul style="color: var(--text-secondary);">
                                    <li>✅ Database Connection</li>
                                    <li>✅ Room Management (CRUD)</li>
                                    <li>✅ Booking Management (CRUD)</li>
                                    <li>✅ User Management</li>
                                    <li>✅ Booking Logic & Availability</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 style="color: var(--text-primary);">Admin Features:</h6>
                                <ul style="color: var(--text-secondary);">
                                    <li>✅ Admin Dashboard with real data</li>
                                    <li>✅ Room editing & status management</li>
                                    <li>✅ Booking confirmation/cancellation</li>
                                    <li>✅ User statistics</li>
                                    <li>✅ Revenue tracking</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../Scripts/bootstrap.bundle.min.js"></script>
</body>
</html>
