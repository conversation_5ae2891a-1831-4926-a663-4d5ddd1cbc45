<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{82761C11-6067-44C2-8623-AF34177E0704}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>QuanLyKhachSan</RootNamespace>
    <AssemblyName>QuanLyKhachSan</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44388</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=*******, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.21.9.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Protobuf.3.21.9\lib\net45\Google.Protobuf.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4, Version=1.3.5.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Compression.LZ4.1.3.5\lib\net462\K4os.Compression.LZ4.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4.Streams, Version=1.3.5.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Compression.LZ4.Streams.1.3.5\lib\net462\K4os.Compression.LZ4.Streams.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Hash.xxHash, Version=1.0.8.0, Culture=neutral, PublicKeyToken=32cd54395057cec3, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Hash.xxHash.1.0.8\lib\net462\K4os.Hash.xxHash.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.5.0.2\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.MSAjax">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.WebForms">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.WebForms.5.0.0\lib\net45\Microsoft.ScriptManager.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Web.Optimization.WebForms">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.WebForms.1.1.3\lib\net45\Microsoft.AspNet.Web.Optimization.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls">
      <HintPath>..\packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net45\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=8.0.33.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.33\lib\net462\MySql.Data.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="About.aspx" />
    <Content Include="Admin\AdminTest.aspx" />
    <Content Include="Admin\Dashboard.aspx" />
    <Content Include="Admin\ManageBookings.aspx" />
    <Content Include="Admin\ManageBookingsAdvanced.aspx" />
    <Content Include="Admin\ManageRooms.aspx" />
    <Content Include="Admin\ManageRoomsAdvanced.aspx" />
    <Content Include="Admin\ManageUsers.aspx" />
    <Content Include="Admin\SimpleAdmin.aspx" />
    <Content Include="Admin\SimpleDashboard.aspx" />
    <Content Include="Admin\DatabaseSetup.aspx" />
    <Content Include="Admin\TestAllFeatures.aspx" />
    <Content Include="Admin\TestBookingLogic.aspx" />
    <Content Include="Admin\TestRoomEdit.aspx" />
    <Content Include="Admin\WorkingDashboard.aspx" />
    <Content Include="BookRoom.aspx" />
    <Content Include="Contact.aspx" />
    <Content Include="Login.aspx" />
    <Content Include="MyBookings.aspx" />
    <Content Include="MyProfile.aspx" />
    <Content Include="Register.aspx" />
    <Content Include="RoomDetails.aspx" />
    <Content Include="RoomList.aspx" />
    <Content Include="Test.aspx" />
    <Content Include="WorkingTest.aspx" />
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-grid.rtl.css" />
    <Content Include="Content\bootstrap-grid.rtl.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap-reboot.rtl.css" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css" />
    <Content Include="Content\bootstrap-utilities.css" />
    <Content Include="Content\bootstrap-utilities.min.css" />
    <Content Include="Content\bootstrap-utilities.rtl.css" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\bootstrap.rtl.css" />
    <Content Include="Content\bootstrap.rtl.min.css" />
    <Content Include="Content\custom-light-theme.css" />
    <Content Include="Content\Site.css" />
    <Content Include="Default.aspx" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.esm.js" />
    <Content Include="Scripts\bootstrap.esm.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.esm.min.js.map" />
    <Content Include="Scripts\bootstrap.esm.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <Content Include="Content\bootstrap.rtl.min.css.map" />
    <Content Include="Content\bootstrap.rtl.css.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.min.css.map" />
    <Content Include="Content\bootstrap-utilities.rtl.css.map" />
    <Content Include="Content\bootstrap-utilities.min.css.map" />
    <Content Include="Content\bootstrap-utilities.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.min.css.map" />
    <Content Include="Content\bootstrap-reboot.rtl.css.map" />
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\bootstrap-reboot.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.min.css.map" />
    <Content Include="Content\bootstrap-grid.rtl.css.map" />
    <Content Include="Content\bootstrap-grid.min.css.map" />
    <Content Include="Content\bootstrap-grid.css.map" />
    <None Include="Scripts\jquery-3.7.0.intellisense.js" />
    <Content Include="Scripts\jquery-3.7.0.js" />
    <Content Include="Scripts\jquery-3.7.0.min.js" />
    <Content Include="Scripts\jquery-3.7.0.slim.js" />
    <Content Include="Scripts\jquery-3.7.0.slim.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\WebForms\DetailsView.js" />
    <Content Include="Scripts\WebForms\Focus.js" />
    <Content Include="Scripts\WebForms\GridView.js" />
    <Content Include="Scripts\WebForms\Menu.js" />
    <Content Include="Scripts\WebForms\MenuStandards.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjax.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxCore.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js" />
    <Content Include="Scripts\WebForms\SmartNav.js" />
    <Content Include="Scripts\WebForms\TreeView.js" />
    <Content Include="Scripts\WebForms\WebForms.js" />
    <Content Include="Scripts\WebForms\WebParts.js" />
    <Content Include="Scripts\WebForms\WebUIValidation.js" />
    <Content Include="Site.Master" />
    <Content Include="ViewSwitcher.ascx" />
    <Content Include="Web.config" />
    <Content Include="Bundle.config" />
    <Content Include="Site.Mobile.Master" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="About.aspx.cs">
      <DependentUpon>About.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="About.aspx.designer.cs">
      <DependentUpon>About.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\AdminTest.aspx.cs">
      <DependentUpon>AdminTest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\AdminTest.aspx.designer.cs">
      <DependentUpon>AdminTest.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\Dashboard.aspx.designer.cs">
      <DependentUpon>Dashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\SimpleDashboard.aspx.cs">
      <DependentUpon>SimpleDashboard.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\SimpleDashboard.aspx.designer.cs">
      <DependentUpon>SimpleDashboard.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="BookRoom.aspx.cs">
      <DependentUpon>BookRoom.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BookRoom.aspx.designer.cs">
      <DependentUpon>BookRoom.aspx</DependentUpon>
    </Compile>
    <Compile Include="BLL\BookingManager.cs" />
    <Compile Include="BLL\RoomManager.cs" />
    <Compile Include="BLL\UserManager.cs" />
    <Compile Include="DAL\BookingDAO.cs" />
    <Compile Include="DAL\DatabaseHelper.cs" />
    <Compile Include="DAL\RoomDAO.cs" />
    <Compile Include="DAL\UserDAO.cs" />
    <Compile Include="Models\Booking.cs" />
    <Compile Include="Models\Room.cs" />
    <Compile Include="Models\User.cs" />
    <Compile Include="Contact.aspx.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Contact.aspx.designer.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="MyBookings.aspx.cs">
      <DependentUpon>MyBookings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MyBookings.aspx.designer.cs">
      <DependentUpon>MyBookings.aspx</DependentUpon>
    </Compile>
    <Compile Include="MyProfile.aspx.cs">
      <DependentUpon>MyProfile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MyProfile.aspx.designer.cs">
      <DependentUpon>MyProfile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Register.aspx.cs">
      <DependentUpon>Register.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Register.aspx.designer.cs">
      <DependentUpon>Register.aspx</DependentUpon>
    </Compile>
    <Compile Include="RoomDetails.aspx.cs">
      <DependentUpon>RoomDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RoomDetails.aspx.designer.cs">
      <DependentUpon>RoomDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="RoomList.aspx.cs">
      <DependentUpon>RoomList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RoomList.aspx.designer.cs">
      <DependentUpon>RoomList.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="Site.Mobile.Master.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Mobile.Master.designer.cs">
      <DependentUpon>Site.Mobile.Master</DependentUpon>
    </Compile>
    <Compile Include="Test.aspx.cs">
      <DependentUpon>Test.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Test.aspx.designer.cs">
      <DependentUpon>Test.aspx</DependentUpon>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ViewSwitcher.ascx.designer.cs">
      <DependentUpon>ViewSwitcher.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <Content Include="Scripts\jquery-3.7.0.slim.min.map" />
    <Content Include="Scripts\jquery-3.7.0.min.map" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != '' And Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' == '' Or !Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets')" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>50215</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:44388/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>