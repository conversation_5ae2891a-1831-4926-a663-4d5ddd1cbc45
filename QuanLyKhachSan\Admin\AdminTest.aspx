<%@ Page Title="Admin Test" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AdminTest.aspx.cs" Inherits="QuanLyKhachSan.AdminTest" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3>
                            <i class="fas fa-shield-alt me-2" style="color: var(--accent-blue);"></i>
                            Admin Test Page
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <strong>Thành công!</strong> Bạn đã truy cập được trang Admin.
                        </div>
                        
                        <h4>Thông tin Session:</h4>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <strong>User ID:</strong> 
                                <asp:Label ID="lblUserId" runat="server" Text="N/A"></asp:Label>
                            </li>
                            <li class="list-group-item">
                                <strong>User Name:</strong> 
                                <asp:Label ID="lblUserName" runat="server" Text="N/A"></asp:Label>
                            </li>
                            <li class="list-group-item">
                                <strong>User Role:</strong> 
                                <asp:Label ID="lblUserRole" runat="server" Text="N/A"></asp:Label>
                            </li>
                        </ul>
                        
                        <hr>
                        
                        <h4>Navigation:</h4>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="Dashboard.aspx" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                            <a href="../Default.aspx" class="btn btn-secondary">
                                <i class="fas fa-home me-2"></i>Trang chủ
                            </a>
                            <a href="../Test.aspx" class="btn btn-info">
                                <i class="fas fa-vial me-2"></i>Test Page
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
