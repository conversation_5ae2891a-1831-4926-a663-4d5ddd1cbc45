{"RootPath": "D:\\CodeThue2025\\QuanLyKhachSan\\QuanLyKhachSan", "ProjectFileName": "QuanLyKhachSan.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "App_Start\\BundleConfig.cs"}, {"SourceFile": "About.aspx.cs"}, {"SourceFile": "About.aspx.designer.cs"}, {"SourceFile": "App_Start\\RouteConfig.cs"}, {"SourceFile": "BookRoom.aspx.cs"}, {"SourceFile": "BookRoom.aspx.designer.cs"}, {"SourceFile": "BLL\\BookingManager.cs"}, {"SourceFile": "BLL\\RoomManager.cs"}, {"SourceFile": "BLL\\UserManager.cs"}, {"SourceFile": "DAL\\BookingDAO.cs"}, {"SourceFile": "DAL\\DatabaseHelper.cs"}, {"SourceFile": "DAL\\RoomDAO.cs"}, {"SourceFile": "DAL\\UserDAO.cs"}, {"SourceFile": "Models\\Booking.cs"}, {"SourceFile": "Models\\Room.cs"}, {"SourceFile": "Models\\User.cs"}, {"SourceFile": "Contact.aspx.cs"}, {"SourceFile": "Contact.aspx.designer.cs"}, {"SourceFile": "Login.aspx.cs"}, {"SourceFile": "Login.aspx.designer.cs"}, {"SourceFile": "Register.aspx.cs"}, {"SourceFile": "Register.aspx.designer.cs"}, {"SourceFile": "RoomDetails.aspx.cs"}, {"SourceFile": "RoomDetails.aspx.designer.cs"}, {"SourceFile": "RoomList.aspx.cs"}, {"SourceFile": "RoomList.aspx.designer.cs"}, {"SourceFile": "Default.aspx.cs"}, {"SourceFile": "Default.aspx.designer.cs"}, {"SourceFile": "Global.asax.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Site.Master.cs"}, {"SourceFile": "Site.Master.designer.cs"}, {"SourceFile": "Site.Mobile.Master.cs"}, {"SourceFile": "Site.Mobile.Master.designer.cs"}, {"SourceFile": "ViewSwitcher.ascx.cs"}, {"SourceFile": "ViewSwitcher.ascx.designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Antlr.3.5.0.2\\lib\\Antlr3.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\\lib\\net45\\Microsoft.AspNet.FriendlyUrls.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Microsoft.AspNet.Web.Optimization.WebForms.1.1.3\\lib\\net45\\Microsoft.AspNet.Web.Optimization.WebForms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\\lib\\net45\\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\\lib\\net45\\Microsoft.ScriptManager.MSAjax.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Microsoft.AspNet.ScriptManager.WebForms.5.0.0\\lib\\net45\\Microsoft.ScriptManager.WebForms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Microsoft.Web.Infrastructure.2.0.0\\lib\\net40\\Microsoft.Web.Infrastructure.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.EnterpriseServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.ApplicationServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.DynamicData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Entity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\Microsoft.AspNet.Web.Optimization.1.1.3\\lib\\net40\\System.Web.Optimization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\CodeThue2025\\QuanLyKhachSan\\packages\\WebGrease.1.6.0\\lib\\WebGrease.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\CodeThue2025\\QuanLyKhachSan\\QuanLyKhachSan\\bin\\QuanLyKhachSan.dll", "OutputItemRelativePath": "QuanLyKhachSan.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}