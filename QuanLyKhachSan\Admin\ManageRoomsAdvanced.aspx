<%@ Page Language="C#" AutoEventWireup="true" %>
    <%@ Import Namespace="QuanLyKhachSan.BLL" %>
        <%@ Import Namespace="QuanLyKhachSan.Models" %>
            <%@ Import Namespace="System.Linq" %>

                <!DOCTYPE html>
                <html>

                <head>
                    <title>Qu<PERSON>n lý phòng - Nâng cao</title>
                    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
                    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
                        rel="stylesheet" />
                    <style>
                        /* Room card improvements */
                        .hover-lift {
                            transition: all 0.3s ease;
                            border: none;
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                        }

                        .hover-lift:hover {
                            transform: translateY(-5px);
                            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                        }

                        /* Navigation improvements */
                        .navbar-nav .nav-link.active {
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 5px;
                            font-weight: 600;
                        }

                        .navbar-nav .nav-link:hover {
                            background-color: rgba(255, 255, 255, 0.1);
                            border-radius: 5px;
                            transition: all 0.3s ease;
                        }

                        .dropdown-menu {
                            border: none;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            border-radius: 8px;
                        }

                        .dropdown-item:hover {
                            background-color: var(--accent-blue);
                            color: white;
                        }

                        /* Room grid improvements */
                        .room-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                            gap: 1.5rem;
                        }

                        @media (min-width: 1200px) {
                            .room-grid {
                                grid-template-columns: repeat(4, 1fr);
                            }
                        }

                        @media (min-width: 992px) and (max-width: 1199px) {
                            .room-grid {
                                grid-template-columns: repeat(3, 1fr);
                            }
                        }

                        @media (min-width: 768px) and (max-width: 991px) {
                            .room-grid {
                                grid-template-columns: repeat(2, 1fr);
                            }
                        }

                        @media (max-width: 767px) {
                            .room-grid {
                                grid-template-columns: 1fr;
                            }
                        }

                        /* Card content improvements */
                        .room-card {
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                        }

                        .room-card .card-body {
                            flex: 1;
                        }

                        .room-card .card-footer {
                            margin-top: auto;
                        }
                    </style>
                </head>

                <body>
                    <script runat="server">
        private RoomManager roomManager;
        private List < Room > rooms;
        private string message = "";
        private string messageClass = "";

        protected void Page_Load(object sender, EventArgs e)
                        {
                            // Check if user is logged in and is admin
                            if (Session["UserId"] == null) {
                                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                                return;
                            }

                            if (Session["UserRole"]?.ToString() != "Admin") {
                                Response.Redirect("../Default.aspx");
                                return;
                            }

                            roomManager = new RoomManager();

                            // Handle form submissions
                            if (Request.HttpMethod == "POST") {
                                HandlePostRequest();
                            }

                            LoadRooms();
                        }

        private void HandlePostRequest()
                        {
            string action = Request.Form["action"];

                            if (action == "toggle_status") {
                                ToggleRoomStatus();
                            }
                            else if (action == "update_room") {
                                UpdateRoom();
                            }
                        }

        private void ToggleRoomStatus()
                        {
                            try {
                int roomId = int.Parse(Request.Form["roomId"]);
                                var room = roomManager.GetRoomById(roomId);

                                if (room != null) {
                                    room.IsAvailable = !room.IsAvailable;
                    bool success = roomManager.UpdateRoom(room);

                                    if (success) {
                                        message = $"Đã cập nhật trạng thái phòng {room.RoomNumber} thành công!";
                                        messageClass = "alert-success";
                                    }
                                    else {
                                        message = "Có lỗi khi cập nhật trạng thái phòng!";
                                        messageClass = "alert-danger";
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                message = "Lỗi: " + ex.Message;
                                messageClass = "alert-danger";
                            }
                        }

        private void UpdateRoom()
                        {
                            try {
                int roomId = int.Parse(Request.Form["roomId"]);
                                var room = roomManager.GetRoomById(roomId);

                                if (room != null) {
                                    room.RoomNumber = Request.Form["roomNumber"];
                                    room.RoomType = Request.Form["roomType"];
                                    room.Price = decimal.Parse(Request.Form["price"]);
                                    room.MaxOccupancy = int.Parse(Request.Form["maxOccupancy"]);
                                    room.Description = Request.Form["description"];
                    
                    bool success = roomManager.UpdateRoom(room);

                                    if (success) {
                                        message = $"Đã cập nhật thông tin phòng {room.RoomNumber} thành công!";
                                        messageClass = "alert-success";
                                    }
                                    else {
                                        message = "Có lỗi khi cập nhật thông tin phòng!";
                                        messageClass = "alert-danger";
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                message = "Lỗi: " + ex.Message;
                                messageClass = "alert-danger";
                            }
                        }

        private void LoadRooms()
                        {
                            try {
                                rooms = roomManager.GetAllRooms();
                            }
                            catch (Exception ex)
                            {
                                rooms = new List < Room > ();
                                System.Diagnostics.Debug.WriteLine($"Error loading rooms: {ex.Message}");
                            }
                        }

        protected string GetStatusBadgeClass(bool isAvailable)
                        {
                            return isAvailable ? "bg-success" : "bg-danger";
                        }

        protected string GetStatusText(bool isAvailable)
                        {
                            return isAvailable ? "Có sẵn" : "Đã đặt";
                        }
                    </script>

                    <div class="container-fluid mt-4">
                        <!-- Admin Navigation -->
                        <nav class="navbar navbar-expand-lg navbar-dark mb-4"
                            style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <div class="container-fluid">
                                <a class="navbar-brand" href="../Default.aspx">
                                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                                </a>

                                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#adminNavbar" aria-controls="adminNavbar" aria-expanded="false"
                                    aria-label="Toggle navigation">
                                    <span class="navbar-toggler-icon"></span>
                                </button>

                                <div class="collapse navbar-collapse" id="adminNavbar">
                                    <ul class="navbar-nav me-auto">
                                        <li class="nav-item">
                                            <a class="nav-link" href="WorkingDashboard.aspx">
                                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link active" href="ManageRoomsAdvanced.aspx">
                                                <i class="fas fa-bed me-1"></i>Quản lý phòng
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" href="ManageBookingsAdvanced.aspx">
                                                <i class="fas fa-calendar-check me-1"></i>Quản lý đặt phòng
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" href="ManageUsers.aspx">
                                                <i class="fas fa-users me-1"></i>Quản lý khách hàng
                                            </a>
                                        </li>
                                    </ul>

                                    <ul class="navbar-nav">
                                        <li class="nav-item dropdown">
                                            <a class="nav-link dropdown-toggle" href="#" id="adminDropdown"
                                                role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-user-shield me-1"></i>Admin
                                            </a>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li>
                                                    <a class="dropdown-item" href="DatabaseSetup.aspx">
                                                        <i class="fas fa-database me-2"></i>Database Setup
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="TestAllFeatures.aspx">
                                                        <i class="fas fa-vial me-2"></i>Test Features
                                                    </a>
                                                </li>
                                                <li>
                                                    <hr class="dropdown-divider">
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="../Default.aspx">
                                                        <i class="fas fa-home me-2"></i>Trang chủ
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="../Login.aspx?action=logout">
                                                        <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                                                    </a>
                                                </li>
                                            </ul>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </nav>

                        <!-- Page Header -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                                            <i class="fas fa-bed me-3" style="color: var(--accent-blue);"></i>
                                            Quản lý phòng - Nâng cao
                                        </h1>
                                        <p class="lead" style="color: var(--text-secondary);">
                                            Quản lý thông tin và trạng thái các phòng với chức năng chỉnh sửa
                                        </p>
                                    </div>
                                    <div>
                                        <a href="WorkingDashboard.aspx" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Quay lại Dashboard
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Alert Messages -->
                        <% if (!string.IsNullOrEmpty(message)) { %>
                            <div class="alert <%= messageClass %> alert-dismissible fade show" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                <%= message %>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <% } %>

                                <!-- Statistics Cards -->
                                <div class="row mb-4">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="stats-card">
                                            <div class="stats-number" style="color: var(--accent-blue);">
                                                <i class="fas fa-bed me-2"></i>
                                                <%= rooms?.Count ?? 0 %>
                                            </div>
                                            <p style="color: var(--text-secondary); font-weight: 500;">Tổng số phòng</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="stats-card">
                                            <div class="stats-number" style="color: var(--accent-mint);">
                                                <i class="fas fa-check-circle me-2"></i>
                                                <%= rooms?.Where(r=> r.IsAvailable).Count() ?? 0 %>
                                            </div>
                                            <p style="color: var(--text-secondary); font-weight: 500;">Phòng trống</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="stats-card">
                                            <div class="stats-number" style="color: var(--accent-orange);">
                                                <i class="fas fa-times-circle me-2"></i>
                                                <%= rooms?.Where(r=> !r.IsAvailable).Count() ?? 0 %>
                                            </div>
                                            <p style="color: var(--text-secondary); font-weight: 500;">Phòng đã đặt</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="stats-card">
                                            <div class="stats-number" style="color: var(--accent-yellow);">
                                                <i class="fas fa-money-bill-wave me-2"></i>
                                                <%= rooms?.Average(r=> r.Price).ToString("N0") ?? "0" %>
                                            </div>
                                            <p style="color: var(--text-secondary); font-weight: 500;">Giá TB (VNĐ)</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Rooms List -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-list me-2" style="color: var(--accent-blue);"></i>
                                                    Danh sách phòng
                                                </h5>
                                                <div>
                                                    <button class="btn btn-success"
                                                        onclick="alert('Chức năng thêm phòng sẽ được phát triển')">
                                                        <i class="fas fa-plus me-2"></i>Thêm phòng
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <% if (rooms !=null && rooms.Any()) { %>
                                                    <div class="row g-4">
                                                        <% foreach (var room in rooms) { %>
                                                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                                                                <div class="card h-100 hover-lift room-card">
                                                                    <div
                                                                        class="card-header d-flex justify-content-between align-items-center">
                                                                        <h6 class="mb-0"
                                                                            style="color: var(--text-primary);">
                                                                            <i class="fas fa-door-open me-2"
                                                                                style="color: var(--accent-blue);"></i>
                                                                            Phòng <%= room.RoomNumber %>
                                                                        </h6>
                                                                        <span
                                                                            class="badge <%= GetStatusBadgeClass(room.IsAvailable) %>">
                                                                            <%= GetStatusText(room.IsAvailable) %>
                                                                        </span>
                                                                    </div>
                                                                    <div class="card-body">
                                                                        <div class="text-center mb-3">
                                                                            <div class="mb-2">
                                                                                <i class="fas fa-bed me-2"
                                                                                    style="color: var(--accent-mint);"></i>
                                                                                <strong
                                                                                    style="color: var(--text-primary);">
                                                                                    <%= room.RoomType %>
                                                                                </strong>
                                                                            </div>
                                                                            <div class="mb-2">
                                                                                <h5 class="text-primary mb-0">
                                                                                    <%= room.Price.ToString("N0") %> VNĐ
                                                                                </h5>
                                                                                <small
                                                                                    style="color: var(--text-secondary);">/
                                                                                    đêm</small>
                                                                            </div>
                                                                            <div class="mb-3">
                                                                                <i class="fas fa-users me-2"
                                                                                    style="color: var(--accent-purple);"></i>
                                                                                <span
                                                                                    style="color: var(--text-primary);">
                                                                                    <%= room.MaxOccupancy %> người
                                                                                </span>
                                                                            </div>
                                                                            <div class="mb-3">
                                                                                <p class="card-text text-muted"
                                                                                    style="font-size: 0.9rem; line-height: 1.4;">
                                                                                    <%= room.Description %>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card-footer bg-transparent border-0 p-3">
                                                                <div class="d-grid gap-2">
                                                                    <button class="btn btn-outline-primary btn-sm"
                                                                        onclick="openEditModal(<%= room.RoomId %>, '<%= room.RoomNumber %>', '<%= room.RoomType %>', <%= room.Price %>, <%= room.MaxOccupancy %>, '<%= room.Description.Replace("'", "\\'") %>')">
                                                                        <i class="fas fa-edit me-2"></i>Chỉnh
                                                                        sửa
                                                                    </button>
                                                                    <% if (room.IsAvailable) { %>
                                                                        <form method="post" style="display: inline;">
                                                                            <input type="hidden" name="action"
                                                                                value="toggle_status" />
                                                                            <input type="hidden" name="roomId"
                                                                                value="<%= room.RoomId %>" />
                                                                            <button type="submit"
                                                                                class="btn btn-outline-warning btn-sm w-100"
                                                                                onclick="return confirm('Bạn có chắc muốn đánh dấu phòng <%= room.RoomNumber %> đã đặt?')">
                                                                                <i class="fas fa-lock me-2"></i>Đánh
                                                                                dấu đã đặt
                                                                            </button>
                                                                        </form>
                                                                        <% } else { %>
                                                                            <form method="post"
                                                                                style="display: inline;">
                                                                                <input type="hidden" name="action"
                                                                                    value="toggle_status" />
                                                                                <input type="hidden" name="roomId"
                                                                                    value="<%= room.RoomId %>" />
                                                                                <button type="submit"
                                                                                    class="btn btn-outline-success btn-sm w-100"
                                                                                    onclick="return confirm('Bạn có chắc muốn đánh dấu phòng <%= room.RoomNumber %> trống?')">
                                                                                    <i
                                                                                        class="fas fa-unlock me-2"></i>Đánh
                                                                                    dấu trống
                                                                                </button>
                                                                            </form>
                                                                            <% } %>
                                                                </div>
                                                            </div>
                                                    </div>
                                            </div>
                                            <% } %>
                                        </div>
                                        <% } else { %>
                                            <div class="text-center py-5">
                                                <i class="fas fa-bed fa-4x mb-4" style="color: var(--text-muted);"></i>
                                                <h4 style="color: var(--text-secondary);">Không có phòng nào
                                                </h4>
                                                <p style="color: var(--text-muted);">Hệ thống chưa có dữ
                                                    liệu phòng hoặc có lỗi kết nối database.</p>
                                            </div>
                                            <% } %>
                                    </div>
                                </div>
                    </div>
                    </div>
                    </div>

                    <!-- Edit Room Modal -->
                    <div class="modal fade" id="editRoomModal" tabindex="-1" aria-labelledby="editRoomModalLabel"
                        aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="editRoomModalLabel">
                                        <i class="fas fa-edit me-2" style="color: var(--accent-blue);"></i>
                                        Chỉnh sửa thông tin phòng
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                                </div>
                                <form method="post" id="editRoomForm">
                                    <div class="modal-body">
                                        <input type="hidden" name="action" value="update_room" />
                                        <input type="hidden" name="roomId" id="editRoomId" />

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="editRoomNumber" class="form-label">
                                                        <i class="fas fa-door-open me-2"
                                                            style="color: var(--accent-blue);"></i>
                                                        Số phòng
                                                    </label>
                                                    <input type="text" class="form-control" id="editRoomNumber"
                                                        name="roomNumber" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="editRoomType" class="form-label">
                                                        <i class="fas fa-bed me-2"
                                                            style="color: var(--accent-mint);"></i>
                                                        Loại phòng
                                                    </label>
                                                    <select class="form-select" id="editRoomType" name="roomType"
                                                        required>
                                                        <option value="">Chọn loại phòng</option>
                                                        <option value="Standard">Standard</option>
                                                        <option value="Deluxe">Deluxe</option>
                                                        <option value="Suite">Suite</option>
                                                        <option value="Family">Family</option>
                                                        <option value="VIP">VIP</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="editPrice" class="form-label">
                                                        <i class="fas fa-money-bill-wave me-2"
                                                            style="color: var(--accent-yellow);"></i>
                                                        Giá phòng (VNĐ)
                                                    </label>
                                                    <input type="number" class="form-control" id="editPrice"
                                                        name="price" min="0" step="1000" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="editMaxOccupancy" class="form-label">
                                                        <i class="fas fa-users me-2"
                                                            style="color: var(--accent-purple);"></i>
                                                        Sức chứa (người)
                                                    </label>
                                                    <input type="number" class="form-control" id="editMaxOccupancy"
                                                        name="maxOccupancy" min="1" max="10" required>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="editDescription" class="form-label">
                                                <i class="fas fa-info-circle me-2"
                                                    style="color: var(--accent-blue);"></i>
                                                Mô tả phòng
                                            </label>
                                            <textarea class="form-control" id="editDescription" name="description"
                                                rows="3" placeholder="Nhập mô tả chi tiết về phòng..."></textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                            <i class="fas fa-times me-2"></i>Hủy
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Lưu thay đổi
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <script src="../Scripts/bootstrap.bundle.min.js"></script>
                    <script>
                        function openEditModal(roomId, roomNumber, roomType, price, maxOccupancy, description) {
                            // Set form values
                            document.getElementById('editRoomId').value = roomId;
                            document.getElementById('editRoomNumber').value = roomNumber;

                            // Set room type with proper selection
                            var roomTypeSelect = document.getElementById('editRoomType');
                            roomTypeSelect.value = roomType;

                            // If value didn't set properly, try setting by option text
                            if (roomTypeSelect.value !== roomType) {
                                for (var i = 0; i < roomTypeSelect.options.length; i++) {
                                    if (roomTypeSelect.options[i].value === roomType || roomTypeSelect.options[i].text === roomType) {
                                        roomTypeSelect.selectedIndex = i;
                                        break;
                                    }
                                }
                            }

                            document.getElementById('editPrice').value = price;
                            document.getElementById('editMaxOccupancy').value = maxOccupancy;
                            document.getElementById('editDescription').value = description;

                            // Update modal title
                            document.getElementById('editRoomModalLabel').innerHTML =
                                '<i class="fas fa-edit me-2" style="color: var(--accent-blue);"></i>Chỉnh sửa phòng ' + roomNumber;

                            // Show modal
                            var modal = new bootstrap.Modal(document.getElementById('editRoomModal'));
                            modal.show();

                            // Debug log to check values
                            console.log('Setting room type:', roomType, 'Selected value:', roomTypeSelect.value);
                        }

                        // Auto-hide alerts after 5 seconds
                        setTimeout(function () {
                            var alerts = document.querySelectorAll('.alert');
                            alerts.forEach(function (alert) {
                                var bsAlert = new bootstrap.Alert(alert);
                                bsAlert.close();
                            });
                        }, 5000);
                    </script>
                </body>

                </html>