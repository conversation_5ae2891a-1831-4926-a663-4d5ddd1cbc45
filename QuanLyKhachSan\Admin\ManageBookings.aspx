<%@ Page Language="C#" AutoEventWireup="true" %>
<%@ Import Namespace="QuanLyKhachSan.BLL" %>
<%@ Import Namespace="QuanLyKhachSan.Models" %>

<!DOCTYPE html>
<html>
<head>
    <title><PERSON>u<PERSON>n lý đặt phòng</title>
    <link href="../Content/bootstrap.min.css" rel="stylesheet" />
    <link href="../Content/custom-light-theme.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
</head>
<body>
    <script runat="server">
        private BookingManager bookingManager;
        private List<Booking> bookings;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Check if user is logged in and is admin
            if (Session["UserId"] == null)
            {
                Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }
            
            if (Session["UserRole"]?.ToString() != "Admin")
            {
                Response.Redirect("../Default.aspx");
                return;
            }

            bookingManager = new BookingManager();
            LoadBookings();
        }

        private void LoadBookings()
        {
            try
            {
                bookings = bookingManager.GetAllBookings().OrderByDescending(b => b.BookingDate).ToList();
            }
            catch (Exception ex)
            {
                bookings = new List<Booking>();
                System.Diagnostics.Debug.WriteLine($"Error loading bookings: {ex.Message}");
            }
        }

        protected string GetStatusBadgeClass(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "bg-success";
                case "pending":
                    return "bg-warning text-dark";
                case "cancelled":
                    return "bg-danger";
                case "completed":
                    return "bg-info";
                default:
                    return "bg-secondary";
            }
        }

        protected string GetStatusText(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "Đã xác nhận";
                case "pending":
                    return "Chờ xác nhận";
                case "cancelled":
                    return "Đã hủy";
                case "completed":
                    return "Hoàn thành";
                default:
                    return status;
            }
        }

        protected string GetStatusColor(string status)
        {
            switch (status?.ToLower())
            {
                case "confirmed":
                    return "#10B981";
                case "pending":
                    return "#F59E0B";
                case "cancelled":
                    return "#EF4444";
                case "completed":
                    return "#3B82F6";
                default:
                    return "#6B7280";
            }
        }

        protected int GetNumberOfNights(DateTime checkIn, DateTime checkOut)
        {
            return (checkOut - checkIn).Days;
        }
    </script>

    <div class="container-fluid mt-4">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="../Default.aspx">
                    <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="SimpleDashboard.aspx">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                    <a class="nav-link" href="../Default.aspx">
                        <i class="fas fa-home me-1"></i>Trang chủ
                    </a>
                </div>
            </div>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                            <i class="fas fa-calendar-check me-3" style="color: var(--accent-blue);"></i>
                            Quản lý đặt phòng
                        </h1>
                        <p class="lead" style="color: var(--text-secondary);">
                            Quản lý và theo dõi tất cả các đặt phòng
                        </p>
                    </div>
                    <div>
                        <a href="SimpleDashboard.aspx" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-blue);">
                        <i class="fas fa-calendar-check me-2"></i>
                        <%= bookings?.Count ?? 0 %>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Tổng đặt phòng</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-mint);">
                        <i class="fas fa-check-circle me-2"></i>
                        <%= bookings?.Where(b => b.Status == "Confirmed").Count() ?? 0 %>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Đã xác nhận</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-yellow);">
                        <i class="fas fa-clock me-2"></i>
                        <%= bookings?.Where(b => b.Status == "Pending").Count() ?? 0 %>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Chờ xác nhận</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number" style="color: var(--accent-orange);">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        <%= bookings?.Where(b => b.Status == "Confirmed" || b.Status == "Completed").Sum(b => b.TotalAmount).ToString("N0") ?? "0" %>
                    </div>
                    <p style="color: var(--text-secondary); font-weight: 500;">Doanh thu (VNĐ)</p>
                </div>
            </div>
        </div>

        <!-- Bookings List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2" style="color: var(--accent-blue);"></i>
                            Danh sách đặt phòng
                        </h5>
                    </div>
                    <div class="card-body">
                        <% if (bookings != null && bookings.Any()) { %>
                            <% foreach (var booking in bookings.Take(20)) { %>
                                <div class="card mb-3 border-start border-4" style="border-left-color: <%= GetStatusColor(booking.Status) %> !important;">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="mb-1" style="color: var(--text-primary);">
                                                        <i class="fas fa-bed me-2" style="color: var(--accent-blue);"></i>
                                                        Phòng <%= booking.Room?.RoomNumber ?? "N/A" %> - <%= booking.Room?.RoomType ?? "N/A" %>
                                                    </h6>
                                                    <span class="badge <%= GetStatusBadgeClass(booking.Status) %>">
                                                        <%= GetStatusText(booking.Status) %>
                                                    </span>
                                                </div>
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <p class="mb-1" style="color: var(--text-secondary); font-size: 0.9rem;">
                                                            <i class="fas fa-user me-2" style="color: var(--accent-mint);"></i>
                                                            Khách hàng: <strong><%= booking.CustomerName %></strong>
                                                        </p>
                                                        <p class="mb-1" style="color: var(--text-secondary); font-size: 0.9rem;">
                                                            <i class="fas fa-envelope me-2" style="color: var(--accent-yellow);"></i>
                                                            Email: <%= booking.CustomerEmail %>
                                                        </p>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <p class="mb-1" style="color: var(--text-secondary); font-size: 0.9rem;">
                                                            <i class="fas fa-calendar-alt me-2" style="color: var(--accent-mint);"></i>
                                                            Nhận: <%= booking.CheckInDate.ToString("dd/MM/yyyy") %>
                                                        </p>
                                                        <p class="mb-1" style="color: var(--text-secondary); font-size: 0.9rem;">
                                                            <i class="fas fa-calendar-check me-2" style="color: var(--accent-orange);"></i>
                                                            Trả: <%= booking.CheckOutDate.ToString("dd/MM/yyyy") %>
                                                        </p>
                                                    </div>
                                                </div>
                                                <p class="mb-1" style="color: var(--text-secondary); font-size: 0.9rem;">
                                                    <i class="fas fa-clock me-2" style="color: var(--accent-purple);"></i>
                                                    Đặt ngày: <%= booking.BookingDate.ToString("dd/MM/yyyy HH:mm") %>
                                                    | <%= GetNumberOfNights(booking.CheckInDate, booking.CheckOutDate) %> đêm
                                                </p>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <h5 class="mb-2" style="color: var(--accent-orange);">
                                                    <%= booking.TotalAmount.ToString("N0") %> VNĐ
                                                </h5>
                                                <div class="btn-group-vertical w-100" role="group">
                                                    <button class="btn btn-outline-primary btn-sm mb-1" 
                                                            onclick="alert('Chi tiết đặt phòng #<%= booking.BookingId %>')">
                                                        <i class="fas fa-eye me-2"></i>Chi tiết
                                                    </button>
                                                    <% if (booking.Status == "Pending") { %>
                                                        <button class="btn btn-outline-success btn-sm mb-1" 
                                                                onclick="alert('Xác nhận đặt phòng #<%= booking.BookingId %>')">
                                                            <i class="fas fa-check me-2"></i>Xác nhận
                                                        </button>
                                                    <% } %>
                                                    <% if (booking.Status == "Confirmed" || booking.Status == "Pending") { %>
                                                        <button class="btn btn-outline-danger btn-sm" 
                                                                onclick="alert('Hủy đặt phòng #<%= booking.BookingId %>')">
                                                            <i class="fas fa-times me-2"></i>Hủy
                                                        </button>
                                                    <% } %>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <% } %>
                            
                            <% if (bookings.Count > 20) { %>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Hiển thị 20 đặt phòng gần nhất. Tổng cộng có <%= bookings.Count %> đặt phòng.
                                </div>
                            <% } %>
                        <% } else { %>
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-4x mb-4" style="color: var(--text-muted);"></i>
                                <h4 style="color: var(--text-secondary);">Không có đặt phòng nào</h4>
                                <p style="color: var(--text-muted);">Hệ thống chưa có dữ liệu đặt phòng hoặc có lỗi kết nối database.</p>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2" style="color: var(--accent-orange);"></i>
                            Thao tác nhanh
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-lg-3 col-md-6">
                                <a href="ManageRooms.aspx" class="btn btn-primary w-100">
                                    <i class="fas fa-bed me-2"></i>Quản lý phòng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="ManageUsers.aspx" class="btn btn-info w-100">
                                    <i class="fas fa-users me-2"></i>Quản lý khách hàng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="../RoomList.aspx" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-eye me-2"></i>Xem trang khách hàng
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <a href="SimpleDashboard.aspx" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../Scripts/bootstrap.bundle.min.js"></script>
</body>
</html>
