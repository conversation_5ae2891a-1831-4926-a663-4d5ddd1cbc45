using System;
using System.Web.UI;
using QuanLyKhachSan.BLL;
using QuanLyKhachSan.Models;

namespace QuanLyKhachSan
{
    public partial class BookRoom : Page
    {
        private RoomManager roomManager;
        private BookingManager bookingManager;
        private UserManager userManager;
        private int roomId;

        protected void Page_Load(object sender, EventArgs e)
        {
            roomManager = new RoomManager();
            bookingManager = new BookingManager();
            userManager = new UserManager();

            // Check if user is logged in
            if (Session["UserId"] == null)
            {
                Response.Redirect("Login.aspx?ReturnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                return;
            }

            // Get room ID from query string
            if (!int.TryParse(Request.QueryString["roomId"], out roomId))
            {
                ShowError("Thông tin phòng không hợp lệ.");
                return;
            }

            if (!IsPostBack)
            {
                LoadRoomInformation();
                LoadCustomerInformation();
                SetBookingDates();
                UpdateBookingSummary();
            }
        }

        private void LoadRoomInformation()
        {
            try
            {
                var room = roomManager.GetRoomById(roomId);
                if (room == null)
                {
                    ShowError("Phòng không tồn tại.");
                    return;
                }

                if (!room.IsAvailable)
                {
                    ShowError("Phòng hiện không khả dụng.");
                    return;
                }

                lblRoomNumber.Text = room.RoomNumber;
                lblRoomType.Text = room.RoomType;
                lblRoomPrice.Text = string.Format("{0:N0}", room.Price);
                lblMaxOccupancy.Text = room.MaxOccupancy.ToString();

                pnlBookingForm.Visible = true;
                pnlError.Visible = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading room information: {ex.Message}");
                ShowError("Có lỗi xảy ra khi tải thông tin phòng.");
            }
        }

        private void LoadCustomerInformation()
        {
            try
            {
                int userId = Convert.ToInt32(Session["UserId"]);
                var user = userManager.GetUserById(userId);
                
                if (user != null)
                {
                    txtCustomerName.Text = user.Name;
                    txtCustomerEmail.Text = user.Email;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading customer information: {ex.Message}");
            }
        }

        private void SetBookingDates()
        {
            // Get dates from query string if available
            string checkInParam = Request.QueryString["checkIn"];
            string checkOutParam = Request.QueryString["checkOut"];

            if (!string.IsNullOrEmpty(checkInParam) && DateTime.TryParse(checkInParam, out DateTime checkIn))
            {
                txtCheckIn.Text = checkIn.ToString("yyyy-MM-dd");
            }
            else
            {
                txtCheckIn.Text = DateTime.Today.ToString("yyyy-MM-dd");
            }

            if (!string.IsNullOrEmpty(checkOutParam) && DateTime.TryParse(checkOutParam, out DateTime checkOut))
            {
                txtCheckOut.Text = checkOut.ToString("yyyy-MM-dd");
            }
            else
            {
                txtCheckOut.Text = DateTime.Today.AddDays(1).ToString("yyyy-MM-dd");
            }
        }

        protected void txtDates_TextChanged(object sender, EventArgs e)
        {
            UpdateBookingSummary();
        }

        private void UpdateBookingSummary()
        {
            try
            {
                if (DateTime.TryParse(txtCheckIn.Text, out DateTime checkIn) && 
                    DateTime.TryParse(txtCheckOut.Text, out DateTime checkOut))
                {
                    if (checkOut <= checkIn)
                    {
                        lblSummaryCheckIn.Text = "-";
                        lblSummaryCheckOut.Text = "-";
                        lblNumberOfNights.Text = "0";
                        lblPricePerNight.Text = "0";
                        lblTotalAmount.Text = "0";
                        return;
                    }

                    var summary = bookingManager.GetBookingSummary(roomId, checkIn, checkOut);
                    if (summary != null)
                    {
                        lblSummaryCheckIn.Text = checkIn.ToString("dd/MM/yyyy");
                        lblSummaryCheckOut.Text = checkOut.ToString("dd/MM/yyyy");
                        lblNumberOfNights.Text = summary["NumberOfNights"].ToString();
                        lblPricePerNight.Text = string.Format("{0:N0}", summary["PricePerNight"]);
                        lblTotalAmount.Text = string.Format("{0:N0}", summary["TotalAmount"]);
                    }
                }
                else
                {
                    lblSummaryCheckIn.Text = "-";
                    lblSummaryCheckOut.Text = "-";
                    lblNumberOfNights.Text = "0";
                    lblPricePerNight.Text = "0";
                    lblTotalAmount.Text = "0";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating booking summary: {ex.Message}");
            }
        }

        protected void btnConfirmBooking_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                try
                {
                    int userId = Convert.ToInt32(Session["UserId"]);
                    
                    if (!DateTime.TryParse(txtCheckIn.Text, out DateTime checkIn) || 
                        !DateTime.TryParse(txtCheckOut.Text, out DateTime checkOut))
                    {
                        ShowMessage("Vui lòng chọn ngày hợp lệ.", "alert-danger");
                        return;
                    }

                    // Validate booking data
                    string validationMessage = bookingManager.ValidateBookingData(roomId, userId, checkIn, checkOut);
                    if (!string.IsNullOrEmpty(validationMessage))
                    {
                        ShowMessage(validationMessage, "alert-danger");
                        return;
                    }

                    // Create booking
                    bool success = bookingManager.CreateBooking(roomId, userId, checkIn, checkOut);

                    if (success)
                    {
                        ShowMessage("Đặt phòng thành công! Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi.", "alert-success");
                        
                        // Disable form controls
                        btnConfirmBooking.Enabled = false;
                        txtCheckIn.Enabled = false;
                        txtCheckOut.Enabled = false;
                        txtCustomerName.Enabled = false;
                        txtCustomerEmail.Enabled = false;
                        txtSpecialRequests.Enabled = false;

                        // Redirect to room list after a short delay
                        Response.AddHeader("REFRESH", "3;URL=RoomList.aspx");
                    }
                    else
                    {
                        ShowMessage("Đặt phòng thất bại. Vui lòng thử lại.", "alert-danger");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error confirming booking: {ex.Message}");
                    ShowMessage("Có lỗi xảy ra trong quá trình đặt phòng. Vui lòng thử lại.", "alert-danger");
                }
            }
        }

        private void ShowError(string message)
        {
            lblError.Text = message;
            pnlError.Visible = true;
            pnlBookingForm.Visible = false;
        }

        private void ShowMessage(string message, string cssClass)
        {
            lblMessage.Text = message;
            pnlMessage.CssClass = $"alert {cssClass}";
            pnlMessage.Visible = true;
        }
    }
}
