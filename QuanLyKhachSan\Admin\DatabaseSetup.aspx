<%@ Page Language="C#" AutoEventWireup="true" %>
    <%@ Import Namespace="System.IO" %>
        <%@ Import Namespace="MySql.Data.MySqlClient" %>

            <!DOCTYPE html>
            <html>

            <head>
                <title>Database Setup</title>
                <link href="../Content/bootstrap.min.css" rel="stylesheet" />
                <link href="../Content/custom-light-theme.css" rel="stylesheet" />
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
                    rel="stylesheet" />
            </head>

            <body>
                <script runat="server">
        private string setupResult = "";

        protected void Page_Load(object sender, EventArgs e)
                    {
                        // Check if user is logged in and is admin
                        if (Session["UserId"] == null) {
                            Response.Redirect("../Login.aspx?returnUrl=" + Server.UrlEncode(Request.Url.ToString()));
                            return;
                        }

                        if (Session["UserRole"]?.ToString() != "Admin") {
                            Response.Redirect("../Default.aspx");
                            return;
                        }

                        if (Request.QueryString["action"] == "setup") {
                            SetupDatabase();
                        }
                        else if (Request.QueryString["action"] == "test") {
                            TestDatabaseConnection();
                        }
                        else if (Request.QueryString["action"] == "update_triggers") {
                            UpdateTriggers();
                        }
                    }

        private void TestDatabaseConnection()
                    {
                        setupResult += "<h4>🔍 Database Connection Test:</h4>";

                        try {
                bool connectionTest = DatabaseHelper.TestConnection();
                            if (connectionTest) {
                                setupResult += "<div class='alert alert-success'>✅ Database connection successful!</div>";

                                // Test basic queries
                                TestBasicQueries();
                            }
                            else {
                                setupResult += "<div class='alert alert-danger'>❌ Database connection failed!</div>";
                            }
                        }
                        catch (Exception ex)
                        {
                            setupResult += $"<div class='alert alert-danger'>❌ Connection error: {ex.Message}</div>";
                        }
                    }

        private void TestBasicQueries()
                    {
                        try {
                // Test if tables exist
                string checkTablesQuery = "SHOW TABLES";
                            var tablesResult = DatabaseHelper.ExecuteQuery(checkTablesQuery);

                            setupResult += $"<div class='alert alert-info'>📊 Found {tablesResult.Rows.Count} tables in database:</div>";
                            setupResult += "<ul class='list-group mb-3'>";

                            foreach(DataRow row in tablesResult.Rows)
                            {
                    string tableName = row[0].ToString();
                                setupResult += $"<li class='list-group-item'>✅ {tableName}</li>";
                            }
                            setupResult += "</ul>";

                            // Test data in each table
                            TestTableData("Users");
                            TestTableData("Rooms");
                            TestTableData("Bookings");

                        }
                        catch (Exception ex)
                        {
                            setupResult += $"<div class='alert alert-warning'>⚠️ Error checking tables: {ex.Message}</div>";
                        }
                    }

        private void TestTableData(string tableName)
                    {
                        try {
                string countQuery = $"SELECT COUNT(*) as RecordCount FROM {tableName}";
                            var result = DatabaseHelper.ExecuteQuery(countQuery);

                            if (result.Rows.Count > 0) {
                    int count = Convert.ToInt32(result.Rows[0]["RecordCount"]);
                                setupResult += $"<div class='alert alert-secondary'>📋 {tableName}: {count} records</div>";
                            }
                        }
                        catch (Exception ex)
                        {
                            setupResult += $"<div class='alert alert-warning'>⚠️ Table {tableName} not found or error: {ex.Message}</div>";
                        }
                    }

        private void SetupDatabase()
                    {
                        setupResult += "<h4>🚀 Database Setup Process:</h4>";

                        try {
                // Read SQL script
                string sqlScriptPath = Server.MapPath("~/Database/CreateDatabase.sql");

                            if (!File.Exists(sqlScriptPath)) {
                                setupResult += "<div class='alert alert-danger'>❌ SQL script file not found at: " + sqlScriptPath + "</div>";
                                return;
                            }
                
                string sqlScript = File.ReadAllText(sqlScriptPath);
                            setupResult += "<div class='alert alert-info'>📄 SQL script loaded successfully</div>";

                            // Split script into individual commands
                            string[] sqlCommands = sqlScript.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                
                int successCount = 0;
                int errorCount = 0;

                            foreach(string command in sqlCommands)
                            {
                    string trimmedCommand = command.Trim();

                                // Skip empty commands and comments
                                if (string.IsNullOrEmpty(trimmedCommand) ||
                                    trimmedCommand.StartsWith("--") ||
                                    trimmedCommand.StartsWith("/*") ||
                                    trimmedCommand.ToUpper().StartsWith("DELIMITER") ||
                                    trimmedCommand.ToUpper().StartsWith("SHOW") ||
                                    trimmedCommand.ToUpper().StartsWith("SELECT")) {
                                    continue;
                                }

                                try {
                                    DatabaseHelper.ExecuteNonQuery(trimmedCommand);
                                    successCount++;

                                    // Log successful important commands
                                    if (trimmedCommand.ToUpper().Contains("CREATE TABLE") ||
                                        trimmedCommand.ToUpper().Contains("INSERT INTO")) {
                            string commandType = trimmedCommand.ToUpper().Contains("CREATE TABLE") ? "Table Created" : "Data Inserted";
                                        setupResult += $"<div class='alert alert-success'>✅ {commandType}: {GetTableNameFromCommand(trimmedCommand)}</div>";
                                    }
                                }
                                catch (Exception ex)
                                {
                                    errorCount++;
                                    setupResult += $"<div class='alert alert-warning'>⚠️ Command failed: {ex.Message}</div>";
                                    setupResult += $"<small class='text-muted'>Command: {trimmedCommand.Substring(0, Math.Min(100, trimmedCommand.Length))}...</small><br>";
                                }
                            }

                            setupResult += $"<div class='alert alert-info'>📊 Setup completed: {successCount} successful, {errorCount} errors</div>";

                            // Test the setup
                            TestDatabaseConnection();

                        }
                        catch (Exception ex)
                        {
                            setupResult += $"<div class='alert alert-danger'>❌ Setup failed: {ex.Message}</div>";
                        }
                    }

        private string GetTableNameFromCommand(string command)
                    {
                        try {
                            if (command.ToUpper().Contains("CREATE TABLE")) {
                                var parts = command.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                                for (int i = 0; i < parts.Length - 1; i++)
                                {
                                    if (parts[i].ToUpper() == "TABLE") {
                                        return parts[i + 1].Replace("(", "").Replace("`", "");
                                    }
                                }
                            }
                            else if (command.ToUpper().Contains("INSERT INTO")) {
                                var parts = command.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                                for (int i = 0; i < parts.Length - 1; i++)
                                {
                                    if (parts[i].ToUpper() == "INTO") {
                                        return parts[i + 1].Replace("(", "").Replace("`", "");
                                    }
                                }
                            }
                            return "Unknown";
                        }
                        catch {
                            return "Unknown";
                        }
                    }
                </script>

                <div class="container mt-4">
                    <!-- Navigation -->
                    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
                        <div class="container-fluid">
                            <a class="navbar-brand" href="../Default.aspx">
                                <i class="fas fa-hotel me-2"></i>Khách sạn ABC
                            </a>
                            <div class="navbar-nav ms-auto">
                                <a class="nav-link" href="WorkingDashboard.aspx">
                                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                </a>
                                <a class="nav-link" href="../WorkingTest.aspx">
                                    <i class="fas fa-vial me-1"></i>Main Test
                                </a>
                            </div>
                        </div>
                    </nav>

                    <!-- Page Header -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h1 class="mb-2" style="color: var(--text-primary); font-weight: 600;">
                                <i class="fas fa-database me-3" style="color: var(--accent-blue);"></i>
                                Database Setup
                            </h1>
                            <p class="lead" style="color: var(--text-secondary);">
                                Thiết lập và kiểm tra database cho hệ thống khách sạn
                            </p>
                        </div>
                    </div>

                    <!-- Setup Controls -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-tools me-2" style="color: var(--accent-mint);"></i>
                                        Database Operations
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex gap-3 flex-wrap">
                                        <a href="?action=test" class="btn btn-primary">
                                            <i class="fas fa-plug me-2"></i>Test Connection
                                        </a>
                                        <a href="?action=setup" class="btn btn-success">
                                            <i class="fas fa-database me-2"></i>Setup Database
                                        </a>
                                        <a href="?action=update_triggers" class="btn btn-warning">
                                            <i class="fas fa-sync me-2"></i>Update Triggers
                                        </a>
                                        <a href="TestAllFeatures.aspx" class="btn btn-info">
                                            <i class="fas fa-vial me-2"></i>Test All Features
                                        </a>
                                        <a href="WorkingDashboard.aspx" class="btn btn-secondary">
                                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Setup Results -->
                    <% if (!string.IsNullOrEmpty(setupResult)) { %>
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-clipboard-check me-2"
                                                style="color: var(--accent-orange);"></i>
                                            Setup Results
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <%= setupResult %>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <% } else { %>
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-body text-center py-5">
                                            <i class="fas fa-database fa-4x mb-4" style="color: var(--text-muted);"></i>
                                            <h4 style="color: var(--text-secondary);">Ready for Database Setup</h4>
                                            <p style="color: var(--text-muted);">Click "Test Connection" to check
                                                database or "Setup Database" to run the SQL script</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <% } %>

                                <!-- SQL Script Info -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-file-code me-2"
                                                        style="color: var(--accent-blue);"></i>
                                                    SQL Script Information
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h6 style="color: var(--text-primary);">Script will create:</h6>
                                                        <ul style="color: var(--text-secondary);">
                                                            <li>✅ Database: HotelBookingDB</li>
                                                            <li>✅ Table: Users (with admin & customer accounts)</li>
                                                            <li>✅ Table: Rooms (with 10 sample rooms)</li>
                                                            <li>✅ Table: Bookings (with sample bookings)</li>
                                                            <li>✅ View: BookingDetails</li>
                                                            <li>✅ Stored Procedures & Triggers</li>
                                                        </ul>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6 style="color: var(--text-primary);">Sample Data Included:
                                                        </h6>
                                                        <ul style="color: var(--text-secondary);">
                                                            <li>🏨 10 rooms (Standard, Deluxe, Suite)</li>
                                                            <li>👤 4 users (1 admin, 3 customers)</li>
                                                            <li>📅 3 sample bookings</li>
                                                            <li>🔐 Default password: "123456" for all users</li>
                                                            <li>👨‍💼 Admin: <EMAIL></li>
                                                        </ul>
                                                    </div>
                                                </div>

                                                <div class="alert alert-info mt-3">
                                                    <h6><i class="fas fa-info-circle me-2"></i>Important Notes:</h6>
                                                    <ul class="mb-0">
                                                        <li>Make sure MySQL server is running</li>
                                                        <li>Update connection string in Web.config if needed</li>
                                                        <li>Script will create database if it doesn't exist</li>
                                                        <li>Existing data will be preserved (script uses IF NOT EXISTS)
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                </div>

                <script src="../Scripts/bootstrap.bundle.min.js"></script>
            </body>

            </html>