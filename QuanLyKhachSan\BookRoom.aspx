<%@ Page Title="Đặt phòng" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="BookRoom.aspx.cs" Inherits="QuanLyKhachSan.BookRoom" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="Default.aspx">Trang chủ</a></li>
                        <li class="breadcrumb-item"><a href="RoomList.aspx">Danh sách phòng</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Đặt phòng</li>
                    </ol>
                </nav>
            </div>
        </div>

        <asp:Panel ID="pnlBookingForm" runat="server" Visible="false">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3>Thông tin đặt phòng</h3>
                        </div>
                        <div class="card-body">
                            <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
                                <asp:Label ID="lblMessage" runat="server"></asp:Label>
                            </asp:Panel>

                            <!-- Room Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5>Thông tin phòng</h5>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Số phòng:</strong> <asp:Label ID="lblRoomNumber" runat="server"></asp:Label></p>
                                                    <p><strong>Loại phòng:</strong> <asp:Label ID="lblRoomType" runat="server"></asp:Label></p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>Giá phòng:</strong> <asp:Label ID="lblRoomPrice" runat="server"></asp:Label> VNĐ/đêm</p>
                                                    <p><strong>Sức chứa:</strong> <asp:Label ID="lblMaxOccupancy" runat="server"></asp:Label> người</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Dates -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5>Thời gian lưu trú</h5>
                                </div>
                                <div class="col-md-6">
                                    <label for="txtCheckIn">Ngày nhận phòng:</label>
                                    <asp:TextBox ID="txtCheckIn" runat="server" CssClass="form-control" TextMode="Date" AutoPostBack="true" OnTextChanged="txtDates_TextChanged"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvCheckIn" runat="server" 
                                        ControlToValidate="txtCheckIn" 
                                        ErrorMessage="Vui lòng chọn ngày nhận phòng" 
                                        CssClass="text-danger" 
                                        Display="Dynamic">
                                    </asp:RequiredFieldValidator>
                                </div>
                                <div class="col-md-6">
                                    <label for="txtCheckOut">Ngày trả phòng:</label>
                                    <asp:TextBox ID="txtCheckOut" runat="server" CssClass="form-control" TextMode="Date" AutoPostBack="true" OnTextChanged="txtDates_TextChanged"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvCheckOut" runat="server" 
                                        ControlToValidate="txtCheckOut" 
                                        ErrorMessage="Vui lòng chọn ngày trả phòng" 
                                        CssClass="text-danger" 
                                        Display="Dynamic">
                                    </asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <!-- Customer Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5>Thông tin khách hàng</h5>
                                </div>
                                <div class="col-md-6">
                                    <label for="txtCustomerName">Họ và tên:</label>
                                    <asp:TextBox ID="txtCustomerName" runat="server" CssClass="form-control" placeholder="Nhập họ và tên"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvCustomerName" runat="server" 
                                        ControlToValidate="txtCustomerName" 
                                        ErrorMessage="Vui lòng nhập họ và tên" 
                                        CssClass="text-danger" 
                                        Display="Dynamic">
                                    </asp:RequiredFieldValidator>
                                </div>
                                <div class="col-md-6">
                                    <label for="txtCustomerEmail">Email:</label>
                                    <asp:TextBox ID="txtCustomerEmail" runat="server" CssClass="form-control" TextMode="Email" placeholder="Nhập email"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvCustomerEmail" runat="server" 
                                        ControlToValidate="txtCustomerEmail" 
                                        ErrorMessage="Vui lòng nhập email" 
                                        CssClass="text-danger" 
                                        Display="Dynamic">
                                    </asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="revCustomerEmail" runat="server" 
                                        ControlToValidate="txtCustomerEmail" 
                                        ErrorMessage="Email không hợp lệ" 
                                        ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" 
                                        CssClass="text-danger" 
                                        Display="Dynamic">
                                    </asp:RegularExpressionValidator>
                                </div>
                            </div>

                            <!-- Special Requests -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <label for="txtSpecialRequests">Yêu cầu đặc biệt (tùy chọn):</label>
                                    <asp:TextBox ID="txtSpecialRequests" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" placeholder="Nhập yêu cầu đặc biệt nếu có..."></asp:TextBox>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="row">
                                <div class="col-12 text-center">
                                    <asp:Button ID="btnConfirmBooking" runat="server" Text="Xác nhận đặt phòng" CssClass="btn btn-success btn-lg" OnClick="btnConfirmBooking_Click" />
                                    <a href="RoomList.aspx" class="btn btn-secondary btn-lg">Hủy bỏ</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Summary -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h4>Tóm tắt đặt phòng</h4>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td>Ngày nhận phòng:</td>
                                    <td><strong><asp:Label ID="lblSummaryCheckIn" runat="server"></asp:Label></strong></td>
                                </tr>
                                <tr>
                                    <td>Ngày trả phòng:</td>
                                    <td><strong><asp:Label ID="lblSummaryCheckOut" runat="server"></asp:Label></strong></td>
                                </tr>
                                <tr>
                                    <td>Số đêm:</td>
                                    <td><strong><asp:Label ID="lblNumberOfNights" runat="server"></asp:Label></strong></td>
                                </tr>
                                <tr>
                                    <td>Giá phòng/đêm:</td>
                                    <td><strong><asp:Label ID="lblPricePerNight" runat="server"></asp:Label> VNĐ</strong></td>
                                </tr>
                                <tr class="table-active">
                                    <td><strong>Tổng tiền:</strong></td>
                                    <td><strong class="text-primary"><asp:Label ID="lblTotalAmount" runat="server"></asp:Label> VNĐ</strong></td>
                                </tr>
                            </table>

                            <div class="alert alert-info">
                                <small>
                                    <i class="fas fa-info-circle"></i>
                                    Giá đã bao gồm thuế và phí dịch vụ.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <asp:Panel ID="pnlError" runat="server" Visible="false">
            <div class="alert alert-danger text-center">
                <h4>Có lỗi xảy ra</h4>
                <p><asp:Label ID="lblError" runat="server"></asp:Label></p>
                <a href="RoomList.aspx" class="btn btn-primary">Quay lại danh sách phòng</a>
            </div>
        </asp:Panel>
    </div>
</asp:Content>
