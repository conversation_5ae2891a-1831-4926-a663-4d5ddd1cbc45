# 🏨 <PERSON>ệ thống Quản lý Đặt phòng Khách sạn

Ứng dụng ASP.NET Web Forms sử dụng C#, kiến trúc 3-layer, kết nối với MySQL để xây dựng một hệ thống đặt phòng khách sạn cơ bản.

## 🔧 Yêu cầu hệ thống

- **Framework**: .NET Framework 4.7.2+
- **IDE**: Visual Studio 2019/2022 hoặc Visual Studio Code
- **Database**: MySQL Server 8.0+
- **Web Server**: IIS Express (tích hợp trong Visual Studio)

## 📁 Cấu trúc dự án

```
QuanLyKhachSan/
├── Models/                 # Entity Classes
│   ├── User.cs
│   ├── Room.cs
│   └── Booking.cs
├── DAL/                    # Data Access Layer
│   ├── DatabaseHelper.cs
│   ├── UserDAO.cs
│   ├── RoomDAO.cs
│   └── BookingDAO.cs
├── BLL/                    # Business Logic Layer
│   ├── UserManager.cs
│   ├── RoomManager.cs
│   └── BookingManager.cs
├── Pages/                  # Web Forms
│   ├── Default.aspx        # Trang chủ
│   ├── Register.aspx       # Đăng ký
│   ├── Login.aspx          # Đăng nhập
│   ├── RoomList.aspx       # Danh sách phòng
│   ├── RoomDetails.aspx    # Chi tiết phòng
│   └── BookRoom.aspx       # Đặt phòng
├── Database/
│   └── CreateDatabase.sql  # Script tạo database
└── Web.config              # Cấu hình ứng dụng
```

## 🚀 Hướng dẫn cài đặt
                                        Email: <EMAIL> / Password: 123456
Email: <EMAIL> / Password: 123456
### 1. Cài đặt Database

1. Mở MySQL Workbench hoặc command line
2. Chạy script `Database/CreateDatabase.sql` để tạo database và dữ liệu mẫu
3. Cập nhật connection string trong `Web.config` nếu cần:

```xml
<connectionStrings>
    <add name="HotelConnectionString" 
         connectionString="Server=localhost;Database=HotelBookingDB;Uid=root;Pwd=your_password;" 
         providerName="MySql.Data.MySqlClient" />
</connectionStrings>
```

### 2. Mở dự án

1. Mở Visual Studio
2. File → Open → Project/Solution
3. Chọn file `QuanLyKhachSan.sln`
4. Build solution (Ctrl+Shift+B)

### 3. Chạy ứng dụng

1. Nhấn F5 hoặc click "Start Debugging"
2. Ứng dụng sẽ mở trong trình duyệt tại `https://localhost:44388/`

## 📊 Database Schema

### Bảng Users
- `UserId` (INT, PK, AUTO_INCREMENT)
- `Name` (VARCHAR(100))
- `Email` (VARCHAR(100), UNIQUE)
- `PasswordHash` (VARCHAR(255))
- `Role` (VARCHAR(20))
- `CreatedDate` (DATETIME)

### Bảng Rooms
- `RoomId` (INT, PK, AUTO_INCREMENT)
- `RoomNumber` (VARCHAR(10), UNIQUE)
- `RoomType` (VARCHAR(50))
- `Price` (DECIMAL(10,2))
- `Description` (TEXT)
- `IsAvailable` (BOOLEAN)
- `ImageUrl` (VARCHAR(255))
- `MaxOccupancy` (INT)

### Bảng Bookings
- `BookingId` (INT, PK, AUTO_INCREMENT)
- `RoomId` (INT, FK)
- `UserId` (INT, FK)
- `CheckInDate` (DATE)
- `CheckOutDate` (DATE)
- `Status` (VARCHAR(20))
- `TotalAmount` (DECIMAL(10,2))
- `BookingDate` (DATETIME)
- `CustomerName` (VARCHAR(100))
- `CustomerEmail` (VARCHAR(100))

## 🎯 Chức năng chính

### Người dùng
- ✅ Đăng ký tài khoản mới
- ✅ Đăng nhập/Đăng xuất
- ✅ Xem danh sách phòng
- ✅ Tìm kiếm phòng theo ngày và loại
- ✅ Xem chi tiết phòng
- ✅ Đặt phòng
- ✅ Tính toán giá phòng tự động

### Validation
- ✅ Required field validation
- ✅ Email format validation
- ✅ Password strength validation
- ✅ Date range validation
- ✅ Room availability checking

## 🔐 Tài khoản mẫu

Sau khi chạy script database, bạn có thể sử dụng các tài khoản sau:

**Admin:**
- Email: `<EMAIL>`
- Password: `123456`

**Customer:**
- Email: `<EMAIL>`
- Password: `123456`

## 🏗️ Kiến trúc 3-Layer

### 1. Presentation Layer (UI)
- **Web Forms (.aspx)**: Giao diện người dùng
- **Code-behind (.aspx.cs)**: Xử lý sự kiện UI
- **Master Page**: Layout chung cho toàn bộ ứng dụng

### 2. Business Logic Layer (BLL)
- **UserManager**: Quản lý người dùng, authentication
- **RoomManager**: Quản lý phòng, tìm kiếm
- **BookingManager**: Quản lý đặt phòng, validation

### 3. Data Access Layer (DAL)
- **DatabaseHelper**: Kết nối database, utility methods
- **UserDAO**: Truy cập dữ liệu người dùng
- **RoomDAO**: Truy cập dữ liệu phòng
- **BookingDAO**: Truy cập dữ liệu đặt phòng

## 🛠️ Công nghệ sử dụng

- **ASP.NET Web Forms**: Framework phát triển web
- **C#**: Ngôn ngữ lập trình
- **MySQL**: Hệ quản trị cơ sở dữ liệu
- **ADO.NET**: Truy cập dữ liệu
- **Bootstrap 5**: CSS Framework
- **jQuery**: JavaScript Library

## 📝 Ghi chú

- Mật khẩu được mã hóa bằng SHA256
- Session được sử dụng để quản lý đăng nhập
- Validation được thực hiện ở cả client-side và server-side
- Responsive design với Bootstrap

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Vui lòng tạo issue hoặc pull request.

## 📄 License

Dự án này được phát hành dưới MIT License.
