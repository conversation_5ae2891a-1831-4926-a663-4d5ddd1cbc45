<%@ Page Title="Chi tiết phòng" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="RoomDetails.aspx.cs" Inherits="QuanLyKhachSan.RoomDetails" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="Default.aspx">Trang chủ</a></li>
                        <li class="breadcrumb-item"><a href="RoomList.aspx">Danh sách phòng</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Chi tiết phòng</li>
                    </ol>
                </nav>
            </div>
        </div>

        <asp:Panel ID="pnlRoomDetails" runat="server" Visible="false">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>Phòng <asp:Label ID="lblRoomNumber" runat="server"></asp:Label></h3>
                            <span class="badge" id="statusBadge" runat="server">
                                <asp:Label ID="lblStatus" runat="server"></asp:Label>
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="room-image mb-3">
                                <asp:Image ID="imgRoom" runat="server" CssClass="img-fluid rounded" AlternateText="Hình ảnh phòng" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4>Thông tin phòng</h4>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Loại phòng:</strong></td>
                                    <td><asp:Label ID="lblRoomType" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td><strong>Giá phòng:</strong></td>
                                    <td><asp:Label ID="lblPrice" runat="server"></asp:Label> VNĐ/đêm</td>
                                </tr>
                                <tr>
                                    <td><strong>Sức chứa:</strong></td>
                                    <td><asp:Label ID="lblMaxOccupancy" runat="server"></asp:Label> người</td>
                                </tr>
                                <tr>
                                    <td><strong>Trạng thái:</strong></td>
                                    <td><asp:Label ID="lblAvailability" runat="server"></asp:Label></td>
                                </tr>
                            </table>

                            <div class="mt-3">
                                <h5>Mô tả:</h5>
                                <p><asp:Label ID="lblDescription" runat="server"></asp:Label></p>
                            </div>

                            <div class="mt-4">
                                <h5>Tiện nghi:</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-wifi"></i> WiFi miễn phí</li>
                                    <li><i class="fas fa-tv"></i> TV màn hình phẳng</li>
                                    <li><i class="fas fa-snowflake"></i> Điều hòa không khí</li>
                                    <li><i class="fas fa-bath"></i> Phòng tắm riêng</li>
                                    <li><i class="fas fa-coffee"></i> Minibar</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Đặt phòng</h4>
                        </div>
                        <div class="card-body">
                            <asp:Panel ID="pnlMessage" runat="server" Visible="false" CssClass="alert">
                                <asp:Label ID="lblMessage" runat="server"></asp:Label>
                            </asp:Panel>

                            <div class="row">
                                <div class="col-md-3">
                                    <label>Ngày nhận phòng:</label>
                                    <asp:TextBox ID="txtCheckIn" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                                </div>
                                <div class="col-md-3">
                                    <label>Ngày trả phòng:</label>
                                    <asp:TextBox ID="txtCheckOut" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                                </div>
                                <div class="col-md-3">
                                    <label>Tổng tiền:</label>
                                    <div class="form-control-plaintext">
                                        <strong><asp:Label ID="lblTotalAmount" runat="server" Text="0 VNĐ"></asp:Label></strong>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label>&nbsp;</label><br />
                                    <asp:Button ID="btnCalculate" runat="server" Text="Tính tiền" CssClass="btn btn-info" OnClick="btnCalculate_Click" />
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12 text-center">
                                    <asp:Button ID="btnBookRoom" runat="server" Text="Đặt phòng ngay" CssClass="btn btn-primary btn-lg" OnClick="btnBookRoom_Click" />
                                    <a href="RoomList.aspx" class="btn btn-secondary btn-lg">Quay lại danh sách</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <asp:Panel ID="pnlRoomNotFound" runat="server" Visible="false">
            <div class="alert alert-warning text-center">
                <h4>Không tìm thấy phòng</h4>
                <p>Phòng bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
                <a href="RoomList.aspx" class="btn btn-primary">Quay lại danh sách phòng</a>
            </div>
        </asp:Panel>
    </div>
</asp:Content>
