using System;

namespace QuanLyKhachSan.Models
{
    public class Booking
    {
        public int BookingId { get; set; }
        public int RoomId { get; set; }
        public int UserId { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public string Status { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime BookingDate { get; set; }
        public string CustomerName { get; set; }
        public string CustomerEmail { get; set; }

        // Navigation properties
        public Room Room { get; set; }
        public User User { get; set; }

        public Booking()
        {
            BookingDate = DateTime.Now;
            Status = "Pending";
        }

        public int GetNumberOfNights()
        {
            return (CheckOutDate - CheckInDate).Days;
        }
    }
}
